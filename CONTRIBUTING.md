# 为笨蛋文档做出贡献

首先,感谢你抽出宝贵时间做出巨大贡献！！

所有类型的更改都会最大限度通过,但在此之前,你需要先阅读以下几点来加快审核进度

## 目录

- [这篇文档有问题](#这篇文档有问题)
- [我想要为文档做出贡献](#我想要为文档做出贡献)
  - [格式要求](#格式要求)
  - [尽量使用个人分叉而不是组织分叉](#尽量使用个人分叉而不是组织分叉)
- [感谢](#感谢)

## 这篇文档有问题

由于此文档由大量人员共同编辑,难免会有瑕疵,如果你发现了文档中的问题之处,请按照以下方法提交：

- 打开 [ISSUE](https://github.com/postyizhan/NitWikit/issues)
- 查看是否有相似问题,无论其处于打开状态还是关闭状态
- 尽可能详细地提出出错的地方和问题详情

然后,我们将尽快处理该问题

## 我想要为文档做出贡献

我们不接受任何无意义或偏离主题的意见,所以你应当说明此更改的合理性,并且你应当在提交更改前查看是否有类似更改

你也可以提供针对[待办事项](https://github.com/postyizhan/NitWikit/blob/main/docs/contribution/todos.md)中未完成事项的更改来提高通过的几率

记住！你的任何更改不止会影响到你,还有其他需要通过这篇文档来搭建服务器的人

我们可以在你说明未排版的前提下代替你进行排版,但在此之前,请遵循以下要求：

### 格式要求

对于文档,我们有格式要求,详见 [格式要求](https://github.com/postyizhan/NitWikit/blob/main/docs/contribution/writing-specification/writing-specification.md)

若格式不规范但不会或不想排版时请在 PR 时提出**文档需排版**,否则我们极大概率会关闭此处更改

### 尽量使用个人分叉而不是组织分叉

我们可能会在一些极端情况下去修改你的分叉,无论是解决大量冲突还是修改错误,这比你自己来回检查要快速且方便的多

问题是,如果你是组织分叉,我们无法将对其进行更改,_详见 isaacs/github#1681_ ,这会导致我们会关闭它并手动合并,而不是被标记为合并

_我们更加希望它会显示为合并而不是被关闭_

#### 感谢

感谢 [contributing-gen](https://contributing.md/) 生成的模板
