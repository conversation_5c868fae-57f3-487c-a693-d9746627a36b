---
title: 如何抵御网络攻击
sidebar_position: 6
---

# 如何抵御网络攻击

随着你的 Minecraft 服务器人数和宣传越来越多，你的服务器越有可能收到其他“友商”或者某些不怀好意的玩家攻击。

别害怕，大多数网络攻击没有那么致命，可能只会引起玩家高 Ping 掉线、后台操作卡顿等。

## 分类

下面将列出几种常见的 Minecraft 服务器容易遭受的攻击类型。

### 应用层 (也称为 L7 )

在服务器上运行并绑定了指定地址和端口的应用程序，可以在这一层接受连接。

应用层的攻击往往意味着针对某个应用程序发起的攻击。

通常攻击者会利用应用中的漏洞，来让应用占用更多的计算机资源，或者通过大量请求使带宽不堪重负，使服务器难以处理新连接。

#### 假人攻击
:::info
本处仅讨论关于 Minecraft 服务器网络安全的概念，不涉及关于“生电”中假人的相关概念。
:::

假人攻击（Fake Player Attack），是指攻击者利用技术手段，模拟正常玩家的行为并向 Minecraft 服务器发起非正常请求，进而使得 Minecraft 服务器因各种原因而瘫痪崩溃或被渗透的一种攻击方式。

假人对服务器造成的影响不尽相同，包括但不限于：
1. 大量假人涌入服务器使服务器达到最大人数限制从而导致正常玩家无法进入服务器。
2. 假人的加入与退出操作可能会导致某些开发不完全的插件在处理此类逻辑时发生内存泄漏和其他非预期的异常。
3. 利用聊天框发送垃圾信息刷屏，扰乱正常消息处理与聊天体验。

在某些程度上，假人攻击类似于 Web 安全中的 [CC 攻击](https://www.qiuwenbaike.cn/wiki/拒绝服务攻击#资源消耗型攻击)。

#### MOTD (状态请求) 攻击

简单来说，就是向服务器请求状态 (也就是 Ping)，玩家每次 Ping 服务器时，服务器将返回一个 MOTD。

由于 MOTD 中包含图片和文字信息，大量的请求会占满服务器带宽，使服务器难以处理新的连接。

Minecraft 后端服务器一般是不会对 Ping 进行过滤和记录的，这会导致 MOTD 攻击难以察觉。

但是对于 Velocity / BungeeCord 等反向代理服务端，默认 Ping 服务器的行为是会被记录的，类似于：

```text
[/127.0.0.1:61647] <-> InitialHandler has pinged
```

:::info

可以通过调整设置 `log_pings` (BungeeCord) 或 `show-ping-requests` (Velocity) 来启用或禁用反向代理在控制台输出 Ping 日志。

:::

#### 其他插件

如果你使用了 Plan，Dynmap 等插件，这些插件会在某个端口开启网站。

请注意这些端口如果被不怀好意的人知道，则可能会导致这些 HTTP 端口遭受攻击。

#### Minecraft 漏洞攻击

通过利用 Minecraft 游戏本身的漏洞，向服务器发送 (可能是大量的) 不合法的数据包，造成服务器卡顿甚至**崩溃**，例如攻击者可以向 BDS 服务器发送大量空数据包使其不断打印日志而不再正常处理游戏内容。  

#### 远程桌面 / SSH 爆破

众所周知，只要足够有耐心，就一定能赢得猜数游戏。远程桌面或 SSH 也是如此。由于其并没有针对用户的复杂的验证机制，这导致任何人都可以无限制地试错。
所以攻击者可以通过编写脚本等方式制作软件，不断地假设密码登录服务器远控服务，这就是**爆破攻击**，或者叫暴力破解密码。最终当登录成功时，攻击即成功，攻击者将**获得你服务器完全的控制权**。

注意由于该服务在电脑上非常常见，所以世界各处经常有攻击者通过扫描 IPv4 地址 (扫段攻击，通过对某个 IP 段的所有 IP 逐个检测来找出可以攻击的目标)，
并通过扫描端口 (扫端口，通过对某个 IP 上的所有端口进行检测来找出可以攻击的目标)，来找到你的服务器的远控地址，
而不需要事先对你的服务器或你本人有任何了解。也就是说，如果你遭遇了这种攻击，不要一味地反思自己是否激怒过他人或和小人有了过节，而是要专注于防范你所遭受的攻击本身。

### 网络层 (也称为 L3 )

网络层攻击是 DDoS 攻击的一种形式，它针对于网络基础架构进行攻击。

最常见的网络层攻击有 ICMP Flood、IP 碎片攻击、IP 欺骗（辅助手段），攻击者可以伪造 IP 地址并向目标服务器发送大量数据包，以消耗目标服务器的网络带宽和系统资源。

防御这种类型的攻击唯一办法就是增大宽带，没有什么别的好办法。

这里列出了一些可行的方法 [网络层与传输层攻击防御](#网络层与传输层攻击防御) 

### 传输层 (也称为 L4 )

Minecraft JAVA 服务端采用 TCP 作为通信协议，所以你可能会遭受到如 TCP Flood (如 SYN Flood、ACK Flood) 等攻击方式。

同上，这里列出了一些可行的方法 [网络层与传输层攻击防御](#网络层与传输层攻击防御) 

## 解决方案

### 低调做人，不要惹是生非

在开服圈子里，有大量的攻击并非无故发起。多数时候是因为服主招惹了一些有攻击能力的人，才招致服务器受到攻击。
你不是专业的企业家、生意人，你的服务器非常脆弱。我的世界作为世界上销量最高的游戏，其玩家社区非常复杂而混乱。如果你对其做不到非常了解，请务必谨慎对待。

1. 遇到对服务器中违规感到愤怒的玩家，不要一味地对他采取强硬态度。尽可能和他私下解决，保证他对处罚结果感到满意，如从轻处罚、删除处罚名单等。
遇到不讲理的玩家，虽然其行为本身不合理，但顾客就是上帝，你仍然需要与其协商做出让步，在对方确实执意要破坏服务器时，再对其采取强硬态度来保证自身利益。注意协商成功后不要和其他任何玩家提及此事。
2. 对已付费、已赞助或活跃的玩家放宽违规行为的评判标准，并优先响应其反馈。愿意为服务器花费真金白银或大把时间的玩家通常更在意服务器，他们的体验往往更重要，不仅后期有潜力为服务器带来更多收入和更多玩家，还很有可能在服务器中遭遇负责情绪时采取更极端的行为。
你可以在这类玩家违反规定后视其影响对其适当采取宽容态度 (睁一只眼闭一只眼)，并在他们和其他玩家起冲突时以协调为主，尽可能不对其进行处罚。
3. 千万不要在其他服务器玩家交流平台 (比如服务器群) 里宣传自己的服务器。如果你的服务器非常缺人，这时突然来人宣传把你的玩家全抢走了，你怎么想？
在其他服务器交流平台上宣传自己的服务器往往会被对方的玩家或管理团队视为不正当竞争，这将激怒他们，并导致他们对你的服务器发起攻击。
4. 不要招惹任何人，包括任何从事或爱好计算机行业的人和所有 mc 玩家。
请注意，很多人嘴上不说自己喜欢攻击他人，但是他们会在愤怒的情况下想尽一切方法搞砸你的服务器，就像他们和其他人闹矛盾时会起肢体冲突一样。

### 开启防火墙

在笔者与其他服主交流的过程中，发现有一些服主会完全关闭服务器的防火墙，其中一些甚至不在操作系统中运行任何安全软件来替代被关闭的防火墙。
我们常说“安全系统最薄弱的地方在于人”，但是这句话并不是说只要保持高度的警惕和拥有丰富的经验就能防范所有的恶意攻击。  
你的服务器上可能存在着大量会自行开启服务的软件。在你没有意识到的情况下，它们很可能被黑客利用，导致你的服务器被攻击。

<details>
  <summary>对于一个我的世界服务器来说，至少有以下类型的软件会发布容易被黑客利用的服务：</summary>

- 位于代理服务端后的子服：  
如果子服并未禁止玩家绕过代理服务端连接子服、你的服务器配置了登录插件、你只通过登录服验证玩家身份而子服没有登录插件，
那么玩家就可以直接从公网连接子服并开始游戏而无需登录。由于没有登录步骤验证身份，玩家甚至可以直接登录管理员帐号，从而炸服。
而一旦开启了防火墙阻止公网连接子服，玩家就只能通过代理端连接登录服完成登录步骤，从而避免了这一威胁。
- 部分插件或软件的远程管理功能：  
有一些软件或服务器插件会默认开启远程管理功能，允许管理员通过网页、ssh 等方式管理它，或者你或其他管理员手动开启了它来方便从服务器后台管理服务器。
然而在没有防火墙的情况下，黑客也可以从公网连接这些远程管理服务。如果它们的密码强度不高，甚至是默认密码或无身份认证，黑客就可以通过操控这些软件来攻击服务器
- MySQL 等通过网络连接的数据库：  
如果你的 MySQL 等数据库不需要来自其他服务器的连接（也就是说你的数据库和服务端都在同一服务器上），而你又没有为 MySQL 设置高安全系数的身份验证，
在没有防火墙的情况下，黑客就可以通过公网直接连接你的 MySQL，窃取或篡改你的重要数据。
- OneBot 服务等对外提供 API 的软件：  
如果你的 OneBot 服务等 API 服务不需要来自其他服务器的连接（也就是说你的机器人和框架都在同一服务器上），而你又没有为这些 服务 设置高安全系数的身份验证，
在没有防火墙的情况下，黑客就可以通过公网多直接连接这些 API 来控制这些软件，比如操纵你的机器人发布不实信息或导致其封号

</details>

#### 如何开启

- Windows：
    - 打开 Windows Defender 防火墙（Win + R 输入 `Firewall.cpl`）
    - 点击侧边栏中的 启用或关闭 Windows Defender 防火墙
    - 把专用网络设置和公用网络设置都调成“启用 Windows Defender 防火墙”，
去掉“阻止所有传入连接，包括位于允许列表中的应用”的勾，
勾上如果“Windows Defender 防火墙阻止新应用时通知我”。
    - 点击下面的确定
- Linux：执行命令 `service iptables start` 开启防火墙

:::tip

Linux 可以通过 `service iptables status` 命令查看 Linux 系统的防火墙开启情况

:::



#### 注意事项

- **开启防火墙后，请检查所有服务器公网服务的放行情况。很多在你关闭防火墙期间新部署的服务都可能没有被防火墙放行。**
- 如果你的服务器不禁 ping，记得放行 ICMP 回显请求相关的服务。

### 使用 Velocity / BungeeCord

不要试图单独使用任何后端服务器 (如 Spigot / Paper / Purpur 等) 抵御大规模应用层攻击。

后端服务器处理连接的速度较慢，这将会导致消耗比代理更多的计算机资源，一旦攻击规模过大，这会导致后端服务器卡顿甚至崩溃。

但对于 Velocity / BungeeCord 等代理服务器，它们被设计为允许接受大量连接，且反向代理自带单个 IP 多次重新连接的配置：

```yaml
connection_throttle: 4000
connection_throttle_limit: 3
```

这意味着，单个 IP 地址在 4000ms 内最多能连接服务器 3 次，如果超过该值，服务器将拒绝此 IP 的任何登入请求，即使该 IP 使用了不同的游戏 ID 尝试加入服务器。

### 在代理端安装反假人插件

你可以在代理端安装假人过滤插件，同样的，由于代理端相较后端服务器在面对大量连接时更加高效，请务必在**代理端**安装插件。

以下是推荐的反机器人插件列表

<!--markdownlint-disable line-length-->

| 名称                                                       | 介绍                             | 支持平台                              | 缺点                            |
|----------------------------------------------------------|--------------------------------|-----------------------------------|-------------------------------|
| [Sonar](https://github.com/jonesdevelopment/sonar)       | 轻量级反机器人，皆在检测和移除机器人，而不影响任何真正的玩家 | Velocity，BungeeCord              | 暂时没有？                         |
| [LimboFilter](https://github.com/Elytrium/LimboFilter) | 强大的过滤机器人方案                     | Velocity                          | 笨重且配置复杂，且仅在必要的时候提供更新。 (缺少维护)  |
| [nAntiBot](https://en.docs.nickuc.com/v/nantibot)        | 一个高效反机器人插件                     | Spigot，Velocity，BungeeCord      | 依赖云服务，无法在服务器网络不好的情况下使用该插件。    |
| [EpicGuard](https://github.com/4drian3d/EpicGuard)       | 基于事件的反机器人和反 VPN 插件               | Waterfall (停止维护)，Paper，Velocity | 容易绕过 (但没那么烦人)，且只支持特定的 Paper 服务端。 |
| [AntiAttackRL](https://github.com/AntiAttackMC/AATRL_Public) | 支持多平台的 AntiBot 插件 | Bukkit，BungeeCord，Folia，Sponge，Velocity 与它们的分支 | 受攻击时新玩家无法直接进入，需要管理员操作; 防御方案过时 |

<!--markdownlint-enable line-length-->

:::warning

该列表目前仅列出了免费的反假人插件，实际情况可能需要使用者自行决定。

使用插件直接对抗超大规模的网络攻击是不太现实的。

如果正在遭受这种攻击，最合理的办法是提升服务器带宽或使用专门针对于此类攻击的代理

:::

### 付费防御核心

如果你非常有钱，你可以打开跨服端[核心选择](https://nitwikit.8aka.org/Java/process/cross-server/server-core-choose)，选择那些付费的跨服端核心，
NullCordX 是一个较好的选择。

但在没有想好的情况下，**不建议为反假人付费**。

### 爆破攻击防御

#### 改掉默认端口

修改默认端口为其他端口。尽可能使用**高位端口**，例如`5000`和`55555`端口尽可能不要使用，`8371`不推荐使用，`36752`推荐使用。

##### [修改 Windows 默认远程桌面端口](https://learn.microsoft.com/zh-cn/windows-server/remote/remote-desktop-services/clients/change-listening-port)

##### [修改 Linux 系统中默认 ssh 端口](https://cloud.tencent.com/developer/article/1635355)

#### 设置强密码

对于容易被爆破的服务，如 MCSM、机器人框架、背包同步软件服务端、远程控制等服务，设置强密码，最好 20 位以上，只要软件允许就要包含：

- 大小写英文
- 数字
- 特殊符号，如`!@#$*_.`

例如不要使用以下密码：

- `123456`(又短又简单，黑客的最爱)  
- `114514`(极常见词汇，114514 是互联网上常见的梗)  
- `#sI3wA@!`(虽然复杂但是由于较短仍然容易被暴力破解)
- `skyworkserver`(服务器名)  
- `yizhan`(人名、物品名、地名等)  
- `111111111111111111111111111`(虽然很长，但是不复杂，仍然容易被破解)
- `minimouse4820030611`(包含生日等个人信息)

并尽可能不使用以下密码：

- `yizhan114514`(由常见词汇组成)
- `shuodedaoli`(汉语拼音而且对应的汉语为常见词汇，本密码对应的汉语词汇为常见的梗“说的道理”)
- `#tianjifuwuqi`(除了寥寥几位强密码以外其他都是弱密码)

#### 在操作系统中安装防御软件

##### Windows

有火绒 (家庭版即自带安全防护无需付费) 等杀毒软件可供选择，宝塔面板也可以购买付费的防爆破功能  

##### Linux

有雷池 waf、1P 等面板可供选择

### 网络层与传输层攻击防御

#### 将服务器托管到高防机房/购买高防 VPS

对于大多数 MC 服务器，150G 的防御是足够的，性价比较高。

一般托管一个月大概 800 RMB，速率为 50 Mbps。

建议最多升级到 300G 防御 (再多就放弃吧这是想让你倒闭的)。

如果是 VPS，建议向 VPS 提供商咨询防御服务。

#### 使用 Minecraft 代理

例如 TCPShield，Cloudflare 和 MineKube

包含专门针对于缓解 Minecraft 攻击的负载均衡代理，且能够有效隐藏服务器 IP 地址。

缺点是似乎还没有任何一家这样的代理拥有国内服务器 (延迟高)，且需要花费一点时间设置

那些在 CF 上 A(或 AAAA，CNAME) 过去到源站 (可能配上 SRV) 就是**自欺欺人，完全没用**，CF 压根不会代理这些端口和协议的流量，开小黄云也一样

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

<Tabs queryString="protect">
<TabItem value="cf-tunnel" label="Cloudflare Tunnel">

Cloudflare 的内网穿透 Tunnel，当高防也是疯了

优点：

- 免费，无需注册
- 296 Tbps 高防，298 位置
- 不限流
- 支持 TCP，UDP，RDP，SSH，HTTP
- SSH 提供 WebSSH，还可以通过 Access 管理
- 自带内网穿透

缺点：

- 延迟较大 (不可以优选)
- 客户端需安装 mod 才能进入 (仅限 TCP，UDP)

</TabItem>
<TabItem value="cf-spectrum" label="Cloudflare Spectrum">

Cloudflare 用于 TCP，UDP 协议的防御，可惜价格太贵了 (1$/GB 抢钱)

优点:

- 296 Tbps 高防，298 位置
- 支持 TCP，UDP，RDP，SSH，HTTP
- 提供 1 个 AnyCast 独立 IPV4
- SSH 提供 WebSSH，还可以通过 Access 管理

缺点：

- 价格太贵了 (CF Pro + 流量计费)(CF Partner 计划早没了)
- 延迟较大 (不可以优选)

价格多贵？

Cloudflare Pro 25$/月 (约合人民币 178 元，免费流量**5GB**)，此后 1$/**GB**

也就是说，1 TB 流量价格为 1044$，约合人民币 7443 元，还不算 CF Pro 订阅费用，真需要可以考虑 Papyrus

</TabItem>
<TabItem value="minekube" label="MineKube">

MineKube 的免费保护，这个组织还有另一个有名作品 Gate

个人感觉比 Cloudflare Tunnel 强很多 (比 Cloudflare Spectrum 体验都好)

优点：

- 免费，无需注册
- 自带高防
- 不限流
- 会提供一个免费域名和 1 个 AnyCast 独立 IPV4
- 有 Dashboard，可以进行网络分流，管理，黑名单等操作
- 自带内网穿透

缺点：

- 延迟非常大
- 没 Geyser 支持

[官网](https://connect.minekube.com/)

</TabItem>
<TabItem value="tcpshield" label="TCPShield">

TCPShield 专业的 Minecraft 网络保护

优点：

- 提供免费套餐 (1TB 免费流量)
- **L7 层保护 (会校验流量合法性)**
- 提供面板管理流量
- 价格便宜

缺点:

- 延迟较大 (启动 Asia Network 后会好很多)

Asia Network(亚洲网络):

- 提供新加坡和东京网络
- 价格:0.01 $/GB(与其他流量分开计费，没有免费流量)(约合人民币 7 分)

Geyser 支持需要 Premium 计划 (100 $/月，堪比抢钱)

Pro 计划 (25$ 每月)(约合人民币 178 元):

- 5 TB 免费流量
- IP 防火墙，可过滤流量

[官网](https://tcpshield.com/)

</TabItem>
<TabItem value="playit" label="Playit.gg(推荐)">

性价比非常高，虽说~~正式用途是朋友联机~~

优点:

- 不限流量
- 支持任意 TCP，UDP 代理
- 提供亚洲节点 (一般来说会被分配到日本节点)
- 提供免费域名
- 自带内网穿透
- 提供防火墙

缺点:

- 没有分析面板
- 绑定自己的域名需要 Premium
- 没有 L7 过滤

Premium 价格:**3 美元**/月 (约合 20 人民币)(非常便宜!)

可以购买独立 IPV4 IP 和 IPV6 IP

</TabItem>
<TabItem value="papyrus" label="Papyrus">

没有免费套餐，但看在 Cloudflare Spectrum 的面子上还是写了

VIP 套餐 (25 $ 每月，约合人民币 177 元):

- 支持 Geyser
- L7 过滤

流量状况:

- 无限流量：仅提供纽约，法兰克福节点
- Cloudflare Spectrum 流量：流量数未知，但不是无限

Enterprise 套餐 (250 $ 每月，约合人民币 1778 元):

- Cloudflare Spectrum 无限流量

:::tip

说句好笑的，Papyrus 官网上说他们有 330 节点，但实际上，Cloudflare 去掉中国节点后只有 298 个节点，算上中国节点后才 330 个

Cloudflare Spectrum 目前是没中国节点的

:::

</TabItem>
</Tabs>

#### 狂套 Frp

这个方法比较缺德，我们只需要疯狂 Frp，一个 Frp 被打死了，我们就换另一个 Frp，通知玩家重新连接就可以。

:::danger

除非迫不得已，不要使用这种方法防御攻击。就算被迫使用这种方法防御攻击，也千万不要告诉任何人，最好对于自己的服务器管理员也闭口不谈，只说“攻击的事我暂时解决了，让玩家进服吧”。
因为在开服圈子的任何一处这种方法都是一种十分自私、对同行极不负责任的行为。
你的行为可能会导致相应的内网穿透运营商突然出现大量额外支出，并影响大量使用同一节点的人正常使用服务 (如果节点被打死了，那么攻击者就是在攻击你的过程中误伤了大量其他无辜用户)。不要因为你图省事的想法牵连无辜的陌生人。

<!--![](_images/angryopenfrp.jpg)  -->

:::

有着闲心还不如用上一条的免费防御，虽然速度慢一点

#### 更换 IP

攻击者需要服务器的 IP 地址才能攻击。

可以不断的更换 IP 地址，打死一个换一个。

还可以使用动态 DNS (DDNS)，换 IP 还能顺便更新 DNS 记录。

:::danger

如果你使用的是腾讯云之类的大厂 VPS，永远不要尝试硬扛 DDOS，服务器受攻击流量超过其机房黑洞阈值时，VPS 会屏蔽服务器的外网访问，直接断网并且恢复时间未知。

:::

#### 反向代理

IPv4 地址的数量是极其有限的，不管是租赁还是托管，服务商通常不会允许你频繁更换 IP 地址（或者得加钱）。

正如前一个方法所言，攻击者需要服务器的 IP 地址才能攻击。但如果我们可以把服务器的地址藏起来呢？

额外租赁一些低价的云服务器做反向代理，所有需要访问源服务器的玩家，都需要通过反向代理才可访问。这会提升一定的延迟，但总比被攻击时进不去服务器要强吧？

依此，我们定义源服务器为「源站」，用于反向代理的低价服务器为「节点」。

这些「节点」由此变为「源站」的挡箭牌，替服务器阻挡攻击流量，只要攻击者没有找到源站的 IP，你的服务器就是相对安全的。  
作为替代，你的节点会承受攻击，达到阈值依然会导致玩家无法进服，此方法仅可让你在攻击下可快速恢复访问，而无需等待黑洞封禁时间结束。

使用这个方法，需要你能找到满足以下特点的云服务器来作为节点，条件可能较为苛刻，没有高防服务器那么烧钱但价格也不太便宜。

- 在中国大陆境内 (尤为重要，除非你想玩家延迟 `200ms+`)
- 稳定
- 相对便宜
- 带宽相对较高

可用于制作反向代理的软件有 `hopper-rs`、`nginx`、`haproxy` 等等，配置正确的情况下，反向代理会进行 IP 地址转发，不会导致服务器显示的玩家 IP 全部为同一个 IP 地址。  
有条件最好自行制作一个快捷安装脚本，以便在节点因攻击被断网时，可快速地再租一台节点服务器部署反向代理。
