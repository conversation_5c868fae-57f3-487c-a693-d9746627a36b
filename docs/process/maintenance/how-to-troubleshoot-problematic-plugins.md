---
title: 如何排除有问题的插件
sidebar_position: 7
---

# 如何排除有问题的插件

当我们更新了服务器一些插件后，可能会发现某些插件出了一些问题，应该如何排除呢？

## log 定位

对于 ERROR 和 WARN 类型的 log 进行分析，查看堆栈报错中是否有某个插件名或者插件的 `.jar` 文件。

将报错先翻译一遍，确定其中是否提示了错误的类型 (如缺少前置，缺少类，插件需要更新等)。

然后你可以尝试按照[如何向大佬求助](https://nitwikit.8aka.org/Java/start/ask-for-help)中提到的方法，增加问题解决的可能性。

## 二分法

二分法的基本思想是通过逐步缩小问题可能出现的范围，直到找到确切的问题所在。

具体操作方面：

1. 保留所有的前置插件，如 Protocolib、Luckperms、Xconomy 等。

2. 将其他插件复制到新文件夹中，删除 `/plugins/` 中非前置插件的 `.jar` 文件。

3. 查看新文件夹中的插件数量，选择其中的一半复制到 `/plugins/` 文件夹中，重启。

4. 如果没有问题，重复上一步；如果出现插件无法加载先查看是否出现 `miss dependency` 即缺少前置。

5. 如果出现问题，说明出问题的插件就在刚才复制的插件中，将确认没问题的插件保留在插件文件夹中，然后再从刚复制的插件中选择一半插件加入插件文件夹中。

6. 重复以上步骤，直到发现某一插件加入前没有问题而加入后出现问题，即可确定为该插件的问题。

:::tip

实际上，不只是插件问题，你遇到的其他很多问题都可以使用二分法让你在不知道原因的情况下锁定问题

:::

## 控制变量法

当需要排查的范围比较小时，相比二分法，控制变量法可能会更合适

具体操作方面：

1. 删/增 一个插件
2. 开启服务端
3. 查看问题是否出现
4. 重复上述步骤

:::tip

同二分法底下的提示，不只是插件问题可以用控制变量法

:::

## Arthas

使用 Arthas 对报错进行分析，[文档](https://nitwikit.8aka.org/arthas)，需要拥有较强的技术力
