---
title: 申请公网 IP
sidebar_position: 5
---
:::tip

申请公网 IP 是一件非常非常头疼的事，你可能需要折腾很多步骤，和运营商扯皮，甚至动用你的人脉。

:::

# 申请公网 IP

<details>
  <summary>为什么要申请公网 IP？</summary>

## IPv4 的枯竭和 IPv6 推广的层层阻力

从 IPv4 的格式可以看出，IPv4 共可以表示 4，294，967，296(40 亿) 个地址。然而由于一些特殊的规定，如 `192.168` 开头的 IP 地址只能用于内网，实际可以分配给互联网上计算机的 IP 地址远比总量要少。
本来 40 亿地址就不够地球上 70 亿人每人一个，更不幸的是，这些 IP 地址的分配还不均匀。美国的国防部和贝尔实验室就拥有上亿个 IPv4 地址，至于整个中国大陆拥有约 4 亿个，虽然很多，但是远不够 14 亿人分配。
为了让 IPv4 够分，出现了 NAT 技术。NAT 的原理是将一些电脑组成一个局域网，然后不给它们分配公网 IP，只让自己拥有公网 IP。那没有在公网上的 IP 地址怎么给访问公网上其他的服务器呢？
NAT 的网关就给局域网中的所有电脑都分配局域网 IP，然后让它们在访问互联网时经过自己。而被访问的服务器也是需要客户端的 IP 地址和端口的，此时 NAT 的网关就将客户端的端口绑定到自己公网 IP 的一个端口，这样对于服务器来说，就是 NAT 网关把自己假扮成了客户端，然后应客户端的要求和服务器进行通信。

这样电脑访问互联网上服务器的需求就解决了，可是如果 NAT 的局域网里有服务器该怎么办？这个服务器没有被分配到公网 IP，其他客户端用什么访问他？NAT  有两种方式可以实现服务器在公网提供服务：
第一种叫端口映射，NAT 网关主动把自己在公网上的一个端口的流量转发到局域网中对应 IP 的指定端口，这样局域网中特定服务器的特定端口就是 NAT 网关的公网 IP 上的一个端口，公网上其他客户端直接访问这个公网 IP 的这个端口，就可以访问到那个服务器上的特定服务了。
这样就实现了 NAT 中的服务器将自己的端口开放到公网。
第二种叫 NAT 打洞，与第一种方法不同的是，第一种 NAT 需要主动把服务器的局域网 IP 上的端口和自己在公网 IP 上的端口进行绑定，而这种方法完全不需要 NAT 主动做任何操作，只需要他进行正常的转换就可以了。
上文提到 NAT 为了让局域网中的电脑访问公网上的服务器，需要把自己的指定端口和指定内网 IP 上的端口之间的流量进行转发，双方就可以进行双向通信了。
那么就可以利用 NAT 的这个特性，首先让局域网中的服务器访问公网上的另一台服务器，让 NAT 去接上这根线，再让客户端也去访问公网上的那台服务器，让它的 NAT 也建立这个连接。
两边连接建立好后，这个服务器告诉双方彼此的 NAT 为这些连接准备的端口号，双方就可以通过彼此的 IP 地址和已经接好的这根线，透过 NAT 愉快地连接了。

随着互联网的发展，越来越多的人拥有了网络终端，而每个网络终端都需要一个 IP 地址来访问互联网。NAT 技术出现后，很多路由器都具备了这个功能。
因此每家只需要在自己办的宽带上接一个有 NAT 功能的路由器，然后把自己所有要上网的设备连接到这个路由器，就可以实现每户人家只需要一个 IPv4 地址。
然而这样的人家越来越多，就算一家只给一个 IPv4 地址然后让所有人自行准备路由器来搭建 NAT，IPv4 地址也已经不够分了。
于是运营商想了一个办法，他们自己准备了 NAT 网关，把几户人家的网线接到这个 NAT 网关下，就实现了这几户共用同一个公网 IP。这下对于运营商来说，公网 IP 终于是够用了。可是对于这几户人家来说，自己搭建服务器却成了难题：
虽然能控制自己的 NAT 网关，却无法控制运营商的 NAT 网关，由于端口映射需要 NAT 网关主动，这个方案就用不了了。
此时 NAT 打洞的方案仍然可行，可是这个方案对于联网用途本身要求很高，首先它需要服务端和客户端软件都支持特定的 NAT 打洞方式，而且它还需要服务端和客户端采用特殊的协议进行通信，对于现有的大部分从未考虑过 NAT 打洞方案的软件来说，这个办法也行不通。
就这样，被接到运营商的 NAT 下的设备就几乎都失去了成为服务器的能力。不过还好 IP 地址没到非常稀缺的程度，现在仍然有一些宽带可以分配到公网 IP，也就能掌握位于公网的 NAT 网关的控制权，并通过端口映射方案在局域网中搭建服务器。

造成以上种种现象的根本原因，其实就是 IPv4 太少了。为了解决这种现象，IPv6  特意加长了表示方式，看那长长的地址就知道，IPv6  的地址肯定是用不完了。事实上，IPv6  的地址一共有 3.4 百万亿亿亿亿个，确实是分不完啊！
难怪会有人说地球上的每一粒沙子都能分配到一个 IPv6 地址。IPv6  的出现打破了层层 NAT 的现状，让每台电脑都能重新获得公网 IP。然而 IPv6 由于和 IPv4 不兼容，在推广时遇到了大难题。
首先是现有的老旧设备需要更新，因为它们不支持新的 IPv6 协议。很多软件也需要大改，因为它们是专为 IPv4 设计的。要从 IPv4 更换到 IPv6，所需要进行的工作极其庞大，所以出现了 IPv4 和 IPv6 共存的方案，即双栈网络。
支持双栈的设备既可以使用 IPv4 工作也可以使用 IPv6 工作，而且为了加速 IPv4 的淘汰，很多设备会刻意地默认使用 IPv6 上网，在 IPv6 无法使用时再使用 IPv4。就这样，一段漫长的过渡阶段开始了。
越来越多的设备和网络开始支持 IPv6，可 IPv4 始 终无法淘汰，因为旧设备和旧软件实在是太多了。

可是近年来，据说 IPv6 的更新速度减缓了，据说主要是运营商尝到了 NAT 的甜头，因为套上运营商 NAT 后，用户就无法自行搭建服务器了，这样用户既不会用家用宽带提供未经审查的服务，也会被迫花更多钱去运营商那里购买昂贵的商用宽带，总地来说就是处处都利好了运营商，让他们既能给自己降低维护成本，又能让用户多给他们花钱。

</details>

<details>
  <summary>开通 IPv6 的方法</summary>

## 开通 IPv6

通常来说国内手机流量 (移动数据) 的网络环境在绝大部分地区已经支持 IPv6。然而由于设备老旧或配置不当等原因，很多宽带 (例如家里办的宽带，网吧、酒店、饭店等公共场所的免费 Wi-Fi ) 都仍然不支持 IPv6。
如果你想知道自己正在使用的网络是否支持 IPv6，可以访问这两个网站：`ipw.cn` 和 `testipv6.cn`，它们会告诉你你现在的网络对 IPv6 的支持情况。如果你的网络还不支持 IPv6，你可能需要自行操作来开启。
由于开通 IPv6 仅仅是一个开关，但是接触到这个开关的途径每个运营商、厂商和路由器品牌的方式都不同。下面仅给出 2024 年一种可能的方式，如果其中有步骤失效，可以上网搜索其他实现该步骤的方法，或者直接上网搜索其他方法操作。

1. 检查是否已经有 IPv6 :如果是 Windows 系统，先打开任务管理器，点击性能选项卡，侧边栏找到以太网点击，下面的信息如果有显示 IPv6 地址，而且前缀不是 `fe80`，那基本就证明是已经有 IPv6 了。
国内绝大多数都是 `2408` `2409` `240e` 开头，如果是其他的你也可以百度一下这个开头，没有说是内网地址或者虚拟机就行
2. 检查宽带是否有开通 IPv6 的条件，如果还不支持 IPv6 那么无法开通，只能换家运营商或者等运营商方面升级。有的时候宽带已经支持 IPv6，但是路由器并没有开启。路由器开启 IPv6，可以百度搜索`路由器品牌 + 怎么开 IPv6`。这里给出常见的华为路由器和 tplink 路由器的开启方法
    - 华为路由器：登录后台之后，点击顶部更多功能，侧边栏找到网络设置点击展开，选择 IPv6，打开开关，下面所有选项全选择自动配置，点击保存。
    切换到侧边栏里面的路由器信息页面，如果 IPv6 那一段显示出来的地址里面有不是 `fe80` 这种的 IPv6 地址 (可能也会有 fe80 开头的，别全都是就行)，那就是成功开启了 IPv6。
    - tplink 路由器：登录后台后点击底部路由设置，侧边栏找到 IPv6 设置点击，IPv6 功能调整为开启，WAN 口连接类型选择自动获取 IP 地址，点击保存。
    找到 IPv6 地址之后，跟上段华为路由器要求的效果一样就行。
如果按照上面的方法设置之后路由器里也显示 fe80 的地址，证明你的光猫也需要配置，或者宽带不支持 IPv6。现在仍然有一些小运营商和一些地区不支持 IPv6，因为网络铺设的成本在那，IPv6 的普及还需要时间。
3. 检查路由器的上网方式：此时先打开路由器的后台，查看路由器的上网方式。不会操作可以百度路由器品牌 + 怎么设置拨号上网。华为路由器点击顶部“我要上网”就可以看到上网方式，TP-Link 路由器点击底部路由设置，侧边栏找到上网设置，就可以看到上网方式。
如果这一步设置的是 DDNS(自动获取 IP 地址)，那就需要修改光猫设置。如果这一步设置的已经是拨号上网 (PPPOE)，那就证明你的运营商没有给你开 IPv6 或者不支持 IPv6。
你就可以直接跳过下面修改光猫的步骤，直接跟运营商报修说要开通 IPv6。
4. 修改光猫为桥接：下面需要修改光猫设置。如果安装师傅没给你超级密码，此时有以下三种解决办法：
    1. (成功率最高，但需要花钱) 在某宝某多搜索“光猫超级密码”，会有很多破解光猫超级密码的服务。这里有没有水深的情况我不清楚，我也没研究过，但是确实有些人在用这个途径，比较有效。
    2. (完全自行操作，社恐患者福音) 在搜索引擎搜索自己的运营商 + 光猫品牌 + 超级密码，然后在光猫后台挨个试。这是一个很漫长的过程，因为每个地区的运营商的默认超级密码都不同，甚至有可能你那边的运营商给你的密码是完全随机的，因此你在网上找到的大多数密码可能都不能用。
有了超级密码之后，登录光猫后台，先记下原来的帐号密码，然后将上网方式修改为桥接。运营商的光猫型号众多，没有主流的后台界面，这里需要大家上网搜索光猫型号 (在光猫的外壳，后台界面，底部的铭牌都有可能看到)+怎么改桥接，并根据实际情况灵活应对。这里对动手能力要求很高，单凭这个教程很难讲明白。
    3. 如果你搞不到超级密码，也可以直接报修，跟安装师傅说你要改桥接。大部分都会直接给改，一部分运营商的安装师傅会说需要给你报备，我家这个就是，我电话说完之后他说要给我报备，报备之后才能改桥接。
5. 使用路由器拨号：光猫改成桥接之后，进入路由器后台，将按上文查看上网方式的方法找到上网方式，改成拨号上网 (PPPoE)，填入在光猫后台找到的帐号密码并保存。
此时你家宽带的质量应该会有所提升，毕竟这次网关给到了路由器，自己的路由器性能一般都比运营商给的那个光猫的要好。此外一部分用联通电信宽带的路由器应该会直接有 IPv4 公网了。如果你是联通电信但仍然没有 IPv4 公网，可以报修说让他给开一下。
一些地方是可以给开的，一些地方会要求额外收费，或者直接不给开，毕竟 IPv4 资源紧缺，运营商能靠商宽捞一笔。这时候再去查看 IPv6 的情况，如果仍然没有，就可能是需要联系运营商开，或者运营商不支持了。
6. 折腾完路由器之后，回到服务器上，任务管理器里应该就显示公网 v6 地址了。要测试这个 v6 是不是真的生效了，可以访问 ipw.cn 或 testipv6.cn 测试。

自此服务器的 IPv6 已经全部配置完毕了。

</details>

## 为家里的宽带申请公网 IPv4

由于 IPv4 地址枯竭，家用宽带中的服务器基本都处于 NAT 网络下。我的世界服务器由于本身不支持 NAT 打洞功能，所以只有端口映射一个办法可用。为了能进行端口映射，你需要获得公网上 NAT 网关的控制权。

### 找到主路由 (NAT 网关)

首先你必须要找到这个 NAT 网关的位置。

现在找到你家里的弱电箱，也就是放“猫”的位置，那里肯定有一个运营商借你们的网络设备“猫”，现在新建的小区楼里一般家用网络都是通过光纤接入互联网，这种连接方式叫光纤入户。如果弱电箱里有一根黄色的细线，那就是光纤，而它连接的“猫”就是“光猫”。

有时光猫上带有天线，自己就可以发射 Wi-Fi 信号，而有的光猫只是一个小盒子，上面需要另外接一个用来发射 Wi-Fi 信号的路由器才能让自己家有 Wi-Fi。

你家网络的 NAT 网关肯定就在光猫和那个路由器之间。

#### 进入路由器后台

为了知道究竟哪个是最上游的 NAT 网关，你必须要进入其中一个设备的后台。网关的后台是一个只能由它下面的设备访问的网站。这个网站可以通过路由器的局域网 IP 地址访问，使用任何一个连接到家里 Wi-Fi 的设备都可以查看这个 IP 地址，而由于不同品牌和系统的设备查看的方法不一样，可以上网搜索“路由器的 ip 地址怎么看”，可以找到大量相关教程。

<details>
  <summary>点击展开 Windows，MacOS，Linux，iOS 和原生安卓的操作教程</summary>

- Windows：打开控制面板 (不知道怎么打开就按 Win+R 然后输入 Ctrl 回车)，点击网络和共享中心，查看活动网络下面会有一个网络几 (可能也叫别的名，反正就会显示一个网络)，点击右边那趟连接右边的蓝字，会弹出窗口“xxx 状态”，
点击详细信息再弹出一个窗口，里面的 IPv4 默认网关就是路由器的 IP 地址。
- MacOS：打开设置 (不知道的就点击菜单栏上苹果菜单展开点系统设置，旧版系统叫系统偏好设置)，点击网络，然后里面哪个绿灯了就点哪个，点进去之后点击右边有一个叫详细信息的按钮，点击会弹窗，左边选择 TCP/IP，右边显示的路由器那个地址就是自己的路由器地址。
- Linux：执行`ip route`命令，第一行输出就是路由器 IP 地址。
- iOS：进入设置，进入 WLAN，自己已经连接的 Wi-Fi 右侧会有一个蓝色的 i，往下翻，路由器那行的 IP 地址就是路由器地址。
- 原生安卓：进入设置，进入网络和互联网，旧版系统进入 WLAN，新版系统进入互联网，点击连接的那个 Wi-Fi 右边的齿轮，往下翻会有一个网关 (如果没有但是有个高级就把那个高级展开)，网关显示的就是路由器的地址。

</details>

把设备通过 Wi-Fi 或者网线连接到家里的宽带网络上后，直接进入浏览器，输入路由器的 IP 地址访问，就可以进入到路由器的后台页面。如果连不上去，首先确认自己是否真正连上了家里的网络，比如手机有没有连上 Wi-Fi，或者连上 Wi-Fi 后有没有因为各种原因而仍然在使用流量。

成功进入后台之后，你首先要确认你进入的这个后台究竟是你自己的路由器，还是那个光猫。如果你进入了光猫的后台，那么网站上一般会标着运营商大大的 logo，如果你进入了路由器，那么网站上一般只会标着路由器品牌的 logo，而没有运营商的 logo。

#### 登录路由器后台

这时路由器一般会提示你输入密码。我相信有很多人设置完这个密码之后就忘了，因为在开我的世界服务器之前，没有人告诉你这个密码很重要。但是忘了是不行的，你必须要知道这个密码。

- 如果是别人设置的，那你就要找到那个人要来密码。
- 如果是装宽带的时候师傅告诉你的，那就联系给你装宽带的那个师傅问密码。
- 如果确实是你自己设置的，而且你实在是想不起来了，~~那可能是这个路由器之前对于你真的不重要吧，~~ 那就只能给路由器重置了。

**注意如果是光猫就无论如何都不要重置！路由器重置后需要重新设置，家里的所有设备都暂时会断网！重置路由器前务必确保你自己或身边的人有能力重新设置这个路由器！**

不同品牌路由器的重置方法不一样，这里不做展开，可以翻出路由器的说明书，或者上网搜索“「路由器品牌」怎么重置”(有些路由器重置非常麻烦，我之前就遇到过一个路由器，我折腾了十分钟才给重置好)。重置完成后，再根据当初安装这个路由器时的设置方法进行设置。

:::info

部分地区的部分运营商可能不允许用户自行登录光猫。如果你遇到了类似的情况，建议直接跳转到 [主动向运营商申请公网 IPv4](#主动向运营商申请公网-ipv4)

:::

#### 查看 WAN IP

登录路由器后台以后，网站上一般会显示一个“WAN IP”，这里的“WAN”代表公网，但它显示的并不一定是公网 IP，用一些小技巧可以判断出来它到底是不是，一共分为三种情况：

1. 显示 `192.168`，不是 NAT 网关
2. 是显示 10  或 100 开头，是 NAT 网关
3. 一种是其他数字开头，是 NAT 网关

如果这个路由器不是 NAT 网关，你就需要换一个路由器重复这些操作，直到它的 WAN IP 符合上述其中一种是 NAT 网关的情况。

### 确认是否已有公网 IPv4

找到 NAT 网关并进入它的后台之后，你就可以操作进行端口映射了。但是等一下！上文还提到，由于部分运营商为了节省公网 IPv4 地址而不给一些家里分配 IPv4 地址，这样的情况下是无法成功进行端口映射的。
因为你家里的 NAT 网关并不是公网上的 NAT 网关，真正公网上的 NAT 网关在运营商手里，**你根本拿不到控制权。你必须先确认一下你的网络是不是这个类型，别白费了工夫。**

方法还是查看那个 WAN IP，如果它不是 10 或 100 开头，你就可以继续进行端口映射了。

### 主动向运营商申请公网 IPv4

但是如果是 10 或 100 开头，就没有办法进行端口映射了吗？其实还是有一丝希望，但是**这需要你大量的努力**，因为你可以通过和运营商交涉让他们给你把 IP 变成真正的公网 IP，这一步就是主动向运营商申请公网 IP。

首先如果你的宽带必须是**联通或电信**运营商，因为目前只有这两个运营商有相对充足的公网 IPv4 地址分配给家用宽带，其他运营商无论如何都不会给你改成真正的公网 IP。

而联通和电信也分地区，现在尤其南方的一些省份由于人口稠密，宽带办理非常多，当地的 IPv4 地址也已经枯竭，所以当地的联通和电信有的对公网 IP 额外收费，有的则是直接不再提供，说什么也不行。
要想和这些运营商申请公网 IP，你可以打电话直接要公网，或者一些省份的电信有自助开通公网 IPv4 的渠道。

通常来说，和运营商申请公网 IP 一般有以下几种情况：

- **路由器已经有公网 IPv4 了**，不需要申请了。
- 运营商说可以有公网 IPv4，**但是你必须得先把 IPv6 停了**。这种情况一般出现在南方部分省份的电信宽带上。
- **运营商要求你进行报备**。有的时候不需要你自己跑去营业厅办手续签字什么的，运营商那边自己给你登记一下就行了。
- **运营商要求带宽达到某个速度以上或资费达到某个标准以上才能给公网 IP。**
- **运营商要求公网要额外收费**。
- 运营商告诉你虽然没有公网，但是他们的**上级 NAT 网关的种类是 NAT1**。
- **运营商打死也不给公网。**

如果联系运营商也解决不了，就没有办法进行端口映射了。可是如果不能进行端口映射，就不能在自己家开服务器了吗？办法总比困难多，下面继续介绍一种特殊但确实有效的办法。

## 商用宽带

上文提到，运营商不愿意推广 IPv6 有可能是因为想让用户给他们交更多的钱买商用宽带。没错，平时我们办理的都是家用宽带，而还有一种宽带可以用于商用，这种宽带一定有公网 IPv4 地址，而且很多宽带都拥有不止一个公网 IP。这种宽带一般用于公司、营业场所、专业服务器机房等。由于是商业用途，这种宽带要**比普通宽带贵得多，而且要求严格，并不是所有人都能办得了**。

要办这种宽带，你必须至少满足以下两个条件：

1. **你自己有一个公司**，或者你能联系到一个公司能够为你办理这种宽带。商用宽带都是以公司为单位办理的，这里的公司必须是已经在相关部分登记，而且有营业执照的那种。
2. **你必须有十分充足的预算**。商用宽带极其昂贵，一般家庭都承担不起，就连企业也要精打细算地用。

满足以上条件之后，你就可以联系运营商办理商用宽带了。

:::tip

一些地区的运营商有时会放出一些低价低配的企业宽带，这些宽带虽然低配，但是上行够高，也一定有公网 IP。如果你的公司预算还是挺紧张的，你可以试着和运营商那边的人处好关系，及时了解到刚放出来的企业宽带并抢购。

:::
