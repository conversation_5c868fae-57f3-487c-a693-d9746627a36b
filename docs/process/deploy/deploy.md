---
title: 部署到生产环境
slug: /deploy
sidebar_position: 1
---

# 概览

**什么是生产环境？**

> 正式对外提供服务的环境，此处指可以让玩家加入到你的服务器的环境

通过前面的学习，你现在已经进入了你本地的服务器，但是我该怎么让其他玩家加入进来呢？

也许你有和朋友联机的经验，知道我们需要有 **公网** 那种东西

不知道也没关系，这是一个通俗易懂的讲解 (不一定正确)：

在开服这一话题中，“公网”一般指“公网 IP”。它就像是你的服务器在网络上的住址，玩家们的游戏需要有这个地址才能找到你的服务器，就像快递员需要知道你家的地址才能送货上门。在继续深入学习和实践开服的过程中，你将经常接触到“公网 IP”这个东西。

而服主们谈论的“有公网”则是指服务器能够将自己的端口直接开放到公网。为了让服务器能开服，你必须保证你的服务器“有公网”，至于如何让服务器有这个“公网”，本章将会进行详尽的介绍。

## 有何建议？

个人建议使用 vps，你会在 [可选方式#vps](optional-mode.md#vps) 了解到相关内容

如果是新手刚起步，那就不要买东西瞎费钱了，自己电脑上做就可，做成之后可以考虑 vps 了

import DocCardList from '@theme/DocCardList';

<DocCardList />
