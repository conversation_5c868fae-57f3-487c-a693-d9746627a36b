---
title: 购置服务器
sidebar_position: 2
---

# 购置服务器

:::danger

低价云超开导致性能下降，不要贪图便宜！

:::

搭建 Minecraft 服务器对服务器的 CPU 性能、内存大小、硬盘 I/O 速度和网络质量，网络带宽均有较高的要求。

## 云服务器

如果你的服务器不是自己购买的而是从服务商处租用的，比如 VPS 或面板。至于这到底是什么，下一章会讲解。

### CPU

#### 核心数

总的说来，核心数量要根据玩家数量，服务器性质，预算选择。

<!--markdownlint-disable line-length-->

由于 Minecraft 的 [Tick-loop](https://nitwikit.8aka.org/start/basic/what-is-caton/) 逻辑是单线程的。更多核心只能用于生成区块、插件任务等非主线程任务，不建议超过 8 核心的服务器继续增加核心提升流畅度。

<!--markdownlint-enable line-length-->

普通插件生存服，人数少于 50 时一般可以使用单端处理，选择核心数量 4 - 8 个。

人数 > 50 推荐使用多端均衡或者 Folia，常见的分生存 x 区的就是多端。这两种方式都可以有效利用多核心。

对于多端，核心数量主要取决于总人数，每 100 玩家多加 4 - 8 个核心是较为合理的。

而 Folia 官方推荐的配置在 16 核心以上，如果是新手或对现有插件有严重依赖的不推荐使用，生态较差。

#### CPU 单核性能

![](_images/购置服务器/cpu.jpg)

一般的，当你搭建是纯净的、不含模组的服务器时，对单核性能的要求较低；当你搭建是含模组的服务器时，对单核性能的要求较高。

CPU 总占用不算高时，Minecraft 的服务器性能与 CPU 单核性能几乎为线性关系，单核性能翻倍几乎就可以多带一倍**甚至更多**的人。

尽量选择不超开的云 (如上图，图中 50% 的性能差就在于低价云超开导致性能下降，不要贪图便宜，服务器到手后要自己跑分)

不超过 20 人时为节省成本可以使用 E5 主机，而单端人数多于 20 基本就意味着需要牺牲玩家的游戏体验或者需要换更好的 CPU。

### 内存

:::info

内存几乎不用考虑 DDR4 / DDR5，高频/低频内存的区别，最重要的是内存大小。

使用高频 DDR5 内存和 DDR4 内存开服基本没有差别，如果是自己买的服务器托管的就省点钱吧。

:::

一般来说，mod 服比插件服占用高，高版本比低版本略高。尽量选择核心数与内存比例在 1:2 以上的套餐 (推荐 1:2 - 1:4 )。

一方面，核心多内存少必然面临性能下降。另一方面，核心多内存少基本为超开，性能会比正常情况下差很多。

大厅服分配 1G - 2G 内存，普通生存服务器或空岛服务器建议分配 8G - 12G，单端人数较多建议使用 12G - 24G 内存。

:::warning

并不是人越多需要的内存越多，不建议分配过多内存，否则导致 GC (内存垃圾回收) 时间过长导致卡顿。

:::

### 带宽

根据玩家数量及视野情况分配。一般情况低版本全默认情况下平均每个玩家会使用 0.25 - 0.4 Mbps 的上行带宽。

1.18 以上服务器平均每人使用 0.3-0.5 Mbps 上行带宽 (一群跑图佬另说) 也就是说一般提供的 10M 带宽够带 15-20 人。

同样的有的服务商会提供所谓“共享上行”，有时会出现其他用户传文件，VPS 所在宿主机所有用户都变卡的情况，建议无论如何搞到一个独享带宽保底。

需要注意的是，想要家庭带宽开服要考虑到吃 DDOS 和部分运营商不提供公网或者公网上行速度非常慢的情况，只建议基友服使用，或者配合 FRP 等流量转发方式使用。

### 系统

Windows 系统一般有 Windows Server，它和普通的 Windows 系统操作没有什么区别，只要用过 Windows 就能快速上手，但是 Windows 系统相对占用系统资源更高。
Linux 系统常见的有 Ubuntu、CentOS 和 Debian。我们日常很少接触 Linux 系统，所以你可能需要事先学习 Linux 系统的操作。Linux 系统占用系统资源更低。

如果希望长期开服，强烈推荐使用 Linux 系统 (如 Ubuntu/CentOS 等)，因为它不仅占用系统资源更低，而且由于其系统本身机制与 Windows 不同，长期运行要比 Windows 系统稳定得多。
很多时候一些服务器软件也仅支持 Linux。

## 物理机

<!--markdownlint-disable no-duplicate-heading-->

如果你的服务器是自己购买的而不是租用的，那就是物理机，家里云和机房的独立机都是物理机。至于这到底是什么，下一章会讲解。

### CPU

通常除非你有明确的需求，不建议为了开 mc 服务器而购买任何 E5(洋垃圾)、至强金银铜牌和 EPYC 的服务器 CPU。优先选用英特尔酷睿和 AMD 锐龙 CPU。由于 mc 服务器对单核要求非常高，所以尽可能选择新款高频版本的 CPU。
区分的方式是英特尔 CPU 后面带 K，比如 10600K、12700KF，AMD 的 CPU 后面带 X，比如 5600X、7950X。级别更高的 CPU 往往核心数和单核性能都更高。要开的服务器规模越大，就越有必要选购更高端的 CPU。
比如要开一个小型的服务器，使用 10300F 即可；要开一个大型的服务器，你可能必须要用 12700KF 才够用。

为服务器选择 CPU 并不需要带核显。核显是 CPU 自带的显卡，可以为主板 I/O 面板上自带的显示接口提供显示输出功能，并提供一定的图形渲染功能。
由于 mc 服务器所有渲染图像的工作都由玩家的设备自行完成，所以服务器不需要任何显卡的性能，你的显卡只需要保证能够进行视频输出即可，也就是“亮机卡”。如果你手里已经有现成的低性能的显卡，你可以直接购买不带核显的 CPU，比如带 F 的英特尔 CPU。
如果没有低性能的 CPU，比如你手里有一张闲置的 2070~~(谁会手里有了 2070 还闲置啊)~~，那么除非你有特殊的需求，比如除了开服还用服务器跑 AI，就不建议把这张显卡放到服务器上使用，这种显卡待机情况下功耗更高，导致你家电表转得更快。

:::warning

做高版本生存服务器，买实体机千万不要考虑 E5 这种洋垃圾，否则你会吃大亏，切记!!!

另外，13700K、13700KF、13900K、13900KF、14700K、14700KF、14900K、14900K 几款 CPU 被曝出由于其主频过高而导致不稳定 (运行软件时软件出错，这可能导致你的服务器崩溃甚至损坏) 的问题，建议开服暂时不使用。

:::

### 主板

虽然 mc 服务器对主板没什么要求，但是 CPU 对主板的要求还是很高的。在选择主板时你需要注意以下几点：

1. 板型。常见的主板从小到大分为 ITX、mATX、ATX、EATX 这几个标准。小的主板尺寸可以装进更小的机箱，大的主板尺寸可以提供更多硬件功能。通常情况下，购买最便宜的 mATX 或 ATX 板型的主板即可，因为你的服务器不需要便携。但是一定要注意**不能大于机箱尺寸，否则机箱装不下**。
2. 主芯片组。主芯片组必须要和 CPU 品牌对应，才能和 CPU 兼容。英特尔有中端的 B 系列和高端的 Z 系列，AMD 有中端的 B 系列和高端的 X 系列。通常不需要考虑主芯片组要选哪个，毕竟你也不需要给服务器主板插一大堆三件。
但是不建议 i9 和 R9 处理器选用中端芯片组，除非你问过确实“能带动”，否则会出现主板供电不足等问题，这是因为中端芯片组的供电模块性能较低。

### 内存

虽然是服务器，但是毕竟游戏服务器，需要的内存也是非常高的。通常不推荐购买“刚好够用”的内存容量，因为你的需求会随着服务器规模的扩大逐渐提高，导致后期你可能需要对内存进行升级或更换。如果不是预算非常紧张，建议先购买 CPU 所支持的最大内存容量的一半，数量为主板内存插槽数量的一半，后期需要扩容时，直接购买**同品牌、同系列、同参数**的内存加装。

mc 服务器对内存的频率和时序几乎没有任何要求 (https://www.minebbs.com/threads/_cpu.17729 )。
所以你可以选择更便宜的低频内存，例如 DDR3 的 1333 MHz、DDR4 的 2666 MHz、DDR5 的 4800 MHz。

由于各内存颗粒工厂的产量 (~~失火频率~~) 不同，内存的价格会随时间出现变化。如果你时间充足，可以多关注数码资讯，选择最合适的时机抢购内存。

### 硬盘

21 世纪 20 年代了，该不会还有人用机械硬盘当系统盘吧。你的服务器至少需要一块固态硬盘作为系统盘，如果固态硬盘容量不大，还最好需要一块机械硬盘专门存放服务器的数据。

主流的硬盘按接口分为 M.2 和 SATA 两种类型。M.2 几乎只有固态硬盘，而 SATA 既可以是固态硬盘也可以是机械硬盘。

选购固态硬盘时，需要注意以下事项：

1. **数据无价！！！**，不要选择小厂品牌的固态硬盘，尽可能选择大品牌，比如三星、长江、铠侠 (东芝)、西数、爱国者、英特尔。希捷和金士顿的固态硬盘虽然性价比不高，但是如果你有明确的需求，也不失为一种选择。
2. 固态硬盘的价格和内存一样存在较大波动。
3. 不建议选择二手固态硬盘！你有很大的可能性买到矿盘（可以理解为 写入量较大的硬盘）。如果非要购买，请注意备份！

选购机械硬盘时，需要注意以下事项：
1. 机械硬盘的 IO 速度 远低于固态硬盘，不要只因为机械硬盘便宜，就去选购机械硬盘！
2. 不要去选择二手机械硬盘，如有购买，建议多备份或组 RAID 使用（RAID 0 除外）！
