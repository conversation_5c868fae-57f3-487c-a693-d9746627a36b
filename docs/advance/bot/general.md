---
title: 通用机器人程序
sidebar_position: 2
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

# 通用机器人程序

指一个独立程序运行的 QQ 机器人程序，这些机器人程序仅只有 QQ 消息处理功能，QQ 连接需要依赖 QQ 机器人框架

部分 QQ 机器人程序会依赖 Minecraft 插件来实现部分无法完成的功能。

关于机器人框架详情请见 [通用 | 机器人框架](https://nitwikit.8aka.org/advance/bot/framework/)

<Tabs queryString="tyqqbot">
<TabItem value="DLS-CQhttp" label="DLS-CQhttp">

| DLS-CQhttp | ![](https://img.shields.io/badge/状态-积极维护-green?style=for-the-badge) |
| --- | --- |
| 相关链接 | [Gitee 仓库](https://gitee.com/dlcn/dlscq) \| [文档站](https://gitee.com/dlcn/dlscq/wikis) |
| 作者 | [21Hertz](https://gitee.com/dlcn) |
| 支持平台 | Java 版和基岩版 |
| 介绍 | 一个无图形服务端管理程序，支持大多数主流服务端，主打长期稳定，使用 OneBot11 标准接入 QQ，理论上可支持任何命令行 |
| 下载 | [Gitee](https://gitee.com/dlcn/dlscq/releases) |

</TabItem>
<TabItem value="DLS-native" label="DLS-native">

| DLS-native | ![](https://img.shields.io/badge/状态-积极维护-green?style=for-the-badge) |
| --- | --- |
| 相关链接 | [Gitee 仓库](https://gitee.com/dlcn/dlsna) \| [文档站](https://gitee.com/dlcn/dlscq/wikis/%E5%85%B6%E4%BB%96%E6%A1%86%E6%9E%B6/%E5%AE%98%E6%96%B9%E9%A2%91%E9%81%93%E6%9C%BA%E5%99%A8%E4%BA%BA) |
| 作者 | [21Hertz](https://gitee.com/dlcn) |
| 支持平台 | Java 版和基岩版 |
| 介绍 | DLS-CQhttp 的一个分支，使用 QQ 开放平台 API 标准接入 QQ，继承了 DLS-CQhttp 一样的易用性 |
| 下载 | [Gitee](https://gitee.com/dlcn/dlsna/releases) |

</TabItem>
<TabItem value="EasyBot" label="EasyBot">

| EasyBot | ![](https://img.shields.io/badge/状态-积极维护-green?style=for-the-badge) |
| --- | --- |
| 相关链接 | [文档站](https://docs.inectar.cn/docs/easybot/intor) |
| 作者 | [easybot-team](https://github.com/easybot-team/) |
| 支持平台 | Java 版 基岩版 MCDR |
| 介绍 | 基于 Blazor 开发的跨平台群服互通框架，使用 OneBot11 标准接入 QQ，配置较为复杂但实现效果较好 |
| 下载 | [官网下载](https://docs.inectar.cn/download/easybot) |

</TabItem>
<TabItem value="Serein" label="Serein">

| Serein | ![](https://img.shields.io/badge/状态-积极维护-green?style=for-the-badge) |
| --- | --- |
| 相关链接 | [Github 仓库](https://github.com/SereinDev/Serein) \| [文档站](https://sereindev.github.io/) |
| 作者 | [SereinDev](https://github.com/SereinDev) |
| 支持平台 | Java 版和基岩版 |
| 介绍 | 本身是一个服务器管理软件，但支持使用 QQ 机器人，使用 OneBot11 标准接入 QQ，有机器人相关配置并且有平台支持插件进行功能拓展 |
| 下载 | [Github](https://github.com/SereinDev/Serein/releases) |

</TabItem>
<TabItem value="CirnoBot" label="CirnoBot">

| CirnoBot | ![](https://img.shields.io/badge/状态-不再积极维护-yellow?style=for-the-badge) |
| --- | --- |
| 相关链接 | [Github 仓库](https://github.com/summerkirakira/CirnoBot) \| [文档站](https://biaoju.site/cirnobot/) |
| 作者 | [summerkirakira](https://github.com/summerkirakira) |
| 支持 **Java 版** | Bukkit 及其分支 |
| 介绍 | 基于 NoneBot2 开发的机器人平台，可使用 OneBot11 标准接入 QQ，有机器人相关配置并且有平台支持插件进行功能拓展 |
| 下载 | [文档教程](https://biaoju.site/cirnobot/docs/%E7%AE%80%E5%8D%95%E4%B8%8A%E6%89%8B) |

</TabItem>
<TabItem value="Minecraft_QQBot" label="Minecraft_QQBot">

| Minecraft_QQBot | ![](https://img.shields.io/badge/状态-积极维护-green?style=for-the-badge) |
| --- | --- |
| 相关链接 | [Github 仓库](https://github.com/Minecraft-QQBot/BotServer) \| [文档站](https://qqbot.bugjump.xyz/) |
| 作者 | [sparkbridge](https://github.com/sparkbridge) |
| 支持 **Java 版** | Bukkit 及其分支 |
| 介绍 | 一款基于 NoneBot2 用多种方式与 Minecraft 交互的 Python QQ 机器人，可使用 OneBot11 标准接入 QQ，有机器人相关配置并且有平台支持插件进行功能拓展 |
| 下载 | [文档教程](https://qqbot.bugjump.xyz/%E6%96%87%E6%A1%A3/%E5%BF%AB%E9%80%9F%E5%BC%80%E5%A7%8B.html) |

</TabItem>
</Tabs>
