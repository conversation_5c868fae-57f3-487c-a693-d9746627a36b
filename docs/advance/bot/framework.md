---
title: 机器人框架
sidebar_position: 1
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

# 机器人框架

## 第三方机器人框架

<Tabs queryString="qqbot">
<TabItem value="LLOneBot" label="LLOneBot">

| LLOneBot | ![](https://img.shields.io/badge/状态-积极维护-green?style=for-the-badge) |
| --- | --- |
| 相关链接 | [Github 仓库](https://github.com/LLOneBot/LLOneBot) \| [文档站](https://llonebot.github.io/zh-CN/) |
| 作者 | [LLOneBot](https://github.com/LLOneBot) |
| 介绍 | LiteLoaderQQNT 插件，实现 OneBot 11 和 Satori 协议，用于 QQ 机器人开发 |
| 下载 | [Github](https://github.com/LLOneBot/LLOneBot/releases) 或者 [LiteLoaderQQNT](https://github.com/LiteLoaderQQNT/LiteLoaderQQNT) 插件市场下载 |

</TabItem>
<TabItem value="NapCatQQ" label="NapCatQQ">

| NapCatQQ | ![](https://img.shields.io/badge/状态-积极维护-green?style=for-the-badge) |
| --- | --- |
| 相关链接 | [Github 仓库](https://github.com/NapNeko/NapCatQQ) \| [文档站](https://napneko.github.io/) |
| 作者 | [NapNeko](https://github.com/NapNeko) |
| 介绍 | NapCatQQ 是现代化的基于 NTQQ 的 Bot 协议端实现 |
| 下载 | [Github](https://github.com/NapNeko/NapCatQQ/releases) 或者 [LiteLoaderQQNT](https://github.com/LiteLoaderQQNT/LiteLoaderQQNT) 插件市场下载 |

</TabItem>
<TabItem value="Lagrange.OneBot" label="Lagrange.OneBot">

| Lagrange.OneBot | ![](https://img.shields.io/badge/状态-积极维护-green?style=for-the-badge) |
| --- | --- |
| 相关链接 | [Github 仓库](https://github.com/LagrangeDev/Lagrange.Core) \| [文档站](https://lagrangedev.github.io/Lagrange.Doc/Lagrange.OneBot/) |
| 作者 | [LagrangeDev](https://github.com/LagrangeDev) |
| 介绍 | Lagrange.Core 是一个开源的 NTQQ 协议实现，实现了 OneBot V11 的通信协议，可以和主流 Bot 框架进行通信 |
| 下载 | [Github](https://github.com/LagrangeDev/Lagrange.Core/releases) |

</TabItem>
<TabItem value="Overflow" label="Overflow">

| Overflow | ![](https://img.shields.io/badge/状态-积极维护-green?style=for-the-badge) |
| --- | --- |
| 相关链接 | [Github 仓库](https://github.com/MrXiaoM/overflow) \| [文档站](https://mirai.mrxiaom.top/docs/UserManual) |
| 作者 | [MrXiaoM](https://github.com/MrXiaoM) |
| 介绍 | Overflow 是 mirai-core-api 的实现，对接 OneBot 11 标准，实现 mirai 的无缝迁移 |
| 下载 | [官网下载](https://mirai.mrxiaom.top/) |

</TabItem>
</Tabs>

## 接入 QQ 官方接口的机器人框架

<Tabs queryString="qqbot">
<TabItem value="Gensokyo" label="Gensokyo">

| Gensokyo | ![](https://img.shields.io/badge/状态-积极维护-green?style=for-the-badge) |
| --- | --- |
| 相关链接 | [Github 仓库](https://github.com/Hoshinonyaruko/Gensokyo) |
| 作者 | [Hoshinonyaruko](https://github.com/Hoshinonyaruko) |
| 介绍 | 基于 OneBot QQ 官方机器人 Api Golang 原生实现 |
| 下载 | [Github](https://github.com/Hoshinonyaruko/Gensokyo/releases) |

</TabItem>
</Tabs>

## 已停止更新的机器人框架

<Tabs queryString="qqbot">
<TabItem value="Shamrock" label="Shamrock">

| Shamrock | ![](https://img.shields.io/badge/状态-停止维护-red?style=for-the-badge) |
| --- | --- |
| 相关链接 | [Github 仓库](https://github.com/whitechi73/OpenShamrock) \| [文档站](https://whitechi73.github.io/OpenShamrock/) |
| 作者 | [whitechi73](https://github.com/whitechi73) |
| 介绍 | 基于 Lsposed（Non-Riru）实现 Kritor 标准的 QQ 机器人框架 |
| 下载 | [Github](https://github.com/whitechi73/OpenShamrock/releases) |

</TabItem>
<TabItem value="chronocat" label="chronocat">

| chronocat | ![](https://img.shields.io/badge/状态-不再积极维护-yellow?style=for-the-badge) |
| --- | --- |
| 相关链接 | [Github 仓库](https://github.com/chrononeko/chronocat) |
| 作者 | [chrononeko](https://github.com/chrononeko) |
| 介绍 | 模块化的 Satori 框架 |
| 下载 | [Github](https://github.com/chrononeko/chronocat/releases) |

</TabItem>
<TabItem value="go-cqhttp" label="go-cqhttp">

| go-cqhttp | ![](https://img.shields.io/badge/状态-停止维护-red?style=for-the-badge) \| [停更公告地址](https://github.com/Mrs4s/go-cqhttp/issues/2471) |
| --- | --- |
| 相关链接 | [Github 仓库](https://github.com/Mrs4s/go-cqhttp) \| [文档站](https://docs.go-cqhttp.org/) |
| 作者 | [Mrs4s](https://github.com/Mrs4s) |
| 介绍 | 基于 Mirai 以及 MiraiGo 的 OneBot Golang 原生实现 |
| 下载 | [Github](https://github.com/Mrs4s/go-cqhttp/releases) |

</TabItem>
<TabItem value="Mirai" label="Mirai">

| Mirai | ![](https://img.shields.io/badge/状态-不再积极维护-yellow?style=for-the-badge) |
| --- | --- |
| 相关链接 | [Github 仓库](https://github.com/mamoe/mirai) \| [官方论坛](https://mirai.mamoe.net/) \| [用户手册](https://github.com/mamoe/mirai/blob/dev/docs/UserManual.md) |
| 作者 | [mamoe](https://github.com/mamoe) |
| 介绍 | mirai 是一个在全平台下运行，提供 QQ Android 协议支持的高效率机器人库 |
| 下载 | [Github](https://github.com/iTXTech/mirai-console-loader/releases) |

</TabItem>
</Tabs>

