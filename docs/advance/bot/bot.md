---
title: 群机器人
slug: /advance/bot
sidebar_position: 6
---

# 群机器人

搭建机器人能够让你实现群服消息互通、查询服务器状态、通过聊天即可管理服务器等功能，让你的玩家交流群与服务器连接起来。<!--运营管理的内容写好后这里做一个链接指向那边的机器人文档，讲解机器人都需要什么功能-->

机器人通常分为两个部分，这两个部分分别负责控制 QQ 和对社交帐号执行程序。

其中与 QQ 通信的部分叫 [框架](framework.md)。机器人程序必须要通过网络或插件等方式与机器人框架连接，然后才能开始控制 QQ 实现对应功能。

当然，部分框架上可以加载扩展，通过这些扩展，你可以实现部分与 MC 本体无关的交互。

对社交账号执行程序的部分可以被叫做机器人程序。但因为本教程针对 MC 服务器，因此以把以服务器插件或类似形式加载的机器人程序称为 [后端插件](plugin.md) ，把以独立程序运行的称为 [通用机器人](general.md) 。

连接的方式目前主流的有 HTTP 协议和 Websocket(ws) 协议。每个协议又分为正向和反向，其中正向要求机器人框架本身作为服务器将端口开放给机器人软件进行连接，而反向则要求机器人软件开放端口给框架连接。

本篇仅列出常见的实现方法，不对具体实现过程描述，请自行查阅相关文档。

## OneBot 标准

[OneBot](https://onebot.dev/) 是**统一的聊天机器人应用接口标准**。它适用于各种各样的即时通讯软件，QQ 是其应用场景之一。如果机器人框架和机器人都支持 OneBot，那它们便可以配合使用。
