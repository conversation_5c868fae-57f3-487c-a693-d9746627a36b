---
title: Linux 开服
slug: /Sundry/Advance/Linux
sidebar_position: 1
---

# Linux 开服

## 为什么选择 Linux 开服

1. 更快的发包速度和接收速度，接入服务器速度将提升数十毫秒起，当面对假人攻击/ DDOS 攻击时会更加稳定；
2. 更好的性能，根据测试 TPS 能提升 8-15 %；
3. 更低的内存占用，对于 Windows 服务器一般开机会占用 1-2 GB 内存，而一般的 Linux 系统开机后占用往往不到 0.5 GB；
4. Linux 的稳定性是 Windows 无论如何也无法比拟的；
5. Linux 安全性更高，开源系统，有社区的支持；
6. Linux 支持文件占用直接删除 (也就是说可以自动更新插件，不像 Windows 那样提示文件被占用)

:::info

可能会有小伙伴说：" Linux 太难操作了，根本不会用。" 其实大多数开 Linux 服务器的服主也不是纯纯使用 Linux 编辑插件配置，那么好，我们为小白推荐三个面板辅助我们开服。

1. [MCSM](https://docs.mcsmanager.com/zh_cn/) 全名 MCSManager 是一款开源，分布式，一键部署，支持 Minecraft 和 Steam 游戏服务器 的控制面板。
2. [1Panel](https://1panel.cn/) 是新一代的 Linux 服务器运维管理面板，支持 Web 图形界面管理、快速建站、应用商店、安全可靠、一键备份等功能。
3. [宝塔面板](https://www.bt.cn/) 是一款支持 Linux 和 Windows 系统的服务器运维管理工具，提供资源监控、文件管理、软件安装等功能。

:::

## 基础指令

在开始开服之前，你需要掌握的 Linux 常用指令

[菜鸟教程 | Linux 需要掌握的一些命令](https://www.runoob.com/w3cnote/linux-useful-command.html)

## 鸣谢

特别感谢 [冰奇](https://github.com/IceBingQI) 提供的本板块的文案及图片

## 另外

“笨蛋脚本”仅支持 Windows 系统，我们暂时没有移植到 Linux 的计划。
