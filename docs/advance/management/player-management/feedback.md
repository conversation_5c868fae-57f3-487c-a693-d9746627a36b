---
title: 玩家反馈
slug: /Sundry/Advance/management/player-management/feedback
sidebar_position: 10
---

# 玩家反馈

## 投票

:::tip

一定不要自顾自地设计服务器内容，你的所有决策都要尽可能保证玩家满意！

:::

虽然玩家们的需求五花八门，但是在你对服务器做出决策时，你可以询问玩家的意见。为了尽可能多地了解玩家的意愿以便给服务器做出让大多数玩家满意的决策，你很可能需要发起投票。

### 投票的设计

投票可以通过以下方式实现：

- 聊天软件内置的投票功能
- 一些服务器插件
- 一个特定的网站

在投票时，一定要确保每个投票者都对应服务器内的一个玩家。例如：

- 在 QQ 群中投票时，你需要确保绝大多数投票者都绑定了服务器内的玩家名。
- 在网站上投票时，你需要让玩家使用和服务器内玩家名相关联的帐号登录来投票。

在设计投票内容时，如果你不确定某个选择题的选项是否覆盖了所有可选的内容，一定要加上“其他”选项，让有相关需求的玩家自己创建选项名。

### 投票的统计

在统计投票内容时，注意投票数据要尽可能与玩家在游戏内的活跃度相关联。在游戏内越活跃的玩家，对游戏内容改变的感知越大。

你可以将玩家在服务器内的活跃数据转换为权重，也就是将玩家的话语权量化，然后利用统计学方法计算出每个选项对于玩家社区真正的影响力。<!--TODO：简单的统计学讲解、如何在 mc 服务器投票中应用统计学-->

### 设定截止日期

在设置投票截止日期时，你可以将其设置在相关工作开始时间后，来同时为服务器团队和玩家换取更多时间。在开始准备相关工作时，先对现有数据进行统计，然后在接下来的时间里，根据统计数据的动态来改变工作重心，直到投票截止。

如果投票功能支持变更截止日期，你还可以先设置较早的截止日期，然后在截止日期最后一天突然延后，这样在初期就能给玩家带来紧迫感，让玩家更积极地投票。例如你可以先设定好截止日期后，在截止日期前催促玩家。然后以“投票人数不够”为由延长投票时间。

这样不仅玩家会抓紧投票，错过第一个截止日期时间的玩家还会心存侥幸，认为你心系玩家社区，并同样积极地参与投票。

:::warning

切勿滥用此方法，否则会导致玩家信任度严重下降。

:::
