---
title: 服务器公告
slug: /Sundry/Advance/management/player-management/announcement
sidebar_position: 7
---

# 服务器公告

<details>
    <summary>在学习正确发布服务器公告前，请停止这些行为</summary>

1. **在服务器内发布无意义公告。**  
例如艾特全体成员说“我要玩原神”。除非你和玩家关系很好，玩家们知道你发的不是公告。
2. **频繁发布公告，尤其是通过机器人频繁地定时艾特全体。**  
例如每天早上艾特全体“早上起床就要来玩服务器！”，上午艾特全体“闲下来就玩服务器！”，中午艾特全体“吃完饭就来玩服务器！”，下午艾特全体“没有事了就来玩服务器！”，晚上艾特全体“睡觉之前来玩服务器！”。这样导致大量的信息充斥在玩家的手机中，影响到他们的日常生活。
3. **发布公告却在公告中不写明任何公告的摘要。**  
例如先发了一条艾特全体成员但什么都不写 (直接写@全体成员)，然后在下一条消息说公告的具体内容，或者是“@全体成员 看公告”这种。这会导致玩家在手机上收到消息时，不知道这条消息对于自己是否重要，就像新闻标题党给新闻起名《科学家发现惊人秘密！你每天都在做的事情可能致命！》、《震惊！某明星竟然在公共场合做出这种事！》，玩家不知道到底该看还是不该看。
4. **发布公告但信息不全或含糊其辞。**  
例如发布“有能力可以赞助服务器”却不写明赞助方式和赞助对象、发布“每天晚上进服务器语音频道”却不写明服务器语音频道是什么，怎么进入。
5. **发布的公告冗长啰嗦。**  
例如发布“众所周知因为我今天下午去我奶家了，然后家里那个服务器不知道因为什么原因就关机了，然后我回去发现是中间停电了，所以下午那阵你们就进不去服务器了”，其实应该写“今天下午服务器因突发停电而关机，现已恢复”。

</details>

## 要点

一个好的服务器公告是玩家快速了解服务器重要通知的前提。**要想发布一条简练易懂的公告，你需要掌握以下要点：**

<details>
    <summary>1. 只叙述玩家关心的内容，不需要包含大量技术细节。</summary>  

例如说“服务器已安装领地插件 Residence，通过/res 指令即可使用”即可  
不需要说“服务器已安装 Residence，这是一款强大的付费插件，拥有强大的权限组功能，目前已经为很多服务器广泛采用”。

</details>

<details>
    <summary>2. 群公告可以幽默，但不能与重要无关的成分占比过高，或过于靠后。</summary>

例如可以说“服务器逆天玩家太多了，这次安装了 CoreProtect，支持局部回档，发现建筑被熊的不要过于担心，找管理说明情况即可，管理会尽力帮你恢复。爱搞事情的小鬼都给我收着点，以后发现一个 ban 一个”。  
而不是说“米米世界玩家入侵我们服务器了？服务器逆天玩家太多，把别人的家全都掀飞了。你们米米玩家不知道米米抄袭我们 mc？简直太猖狂了，我要把你们通通制裁！现在我还可以发动服主之力，把你们被毁的家恢复原样，太神奇了！”。  
如果你希望通过幽默和玩家保持良好关系，建议尽可能在聊天中展现，而不是让服务器到处都充满“逆天”发言，导致玩家无法正常地接收服务器的信息。

</details>

<details>
    <summary>3. 尽可能少发公告，避免浪费玩家时间。</summary>

只要不涉及需要立即发布的公告，就最好挑选合适的时间，将过去几条要发布的公告合并发布。例如你的服务器上午安装了一个插件，中午新增了一条规定，你可以在中午新增规定后再发布“服务器中新增功能 xxx，使用文档 xxx。
另外服务器中新增了规定 xxx，也就是说你必须 xxx，否则会受到 xxx 处罚。”  
而不是上午艾特全体成员说服务器新装了插件，下午又艾特全体成员说服务器有了新规定。  
只有当公告需要立即通知玩家时再将公告单独发布，例如“服务器末地将于明天晚上 8 点重置，请各位玩家及时拿走存放在末地的贵重物品”。

</details>

<details>
    <summary>4. 公告中不要带有不友好的语言。  </summary>

比如你不能说“@全体成员 哪个＊＊养村民没完了？服务器都卡出史了，你养你＊呢？”。

</details>

## 形式

公告发布有以下两种方式可以选择：

1. **将公告立即推送至所有人** ，如 QQ 的艾特全体成员，或者服务器的`/title`指令。  
由于这可能打扰到玩家的日常生活，所以只推荐公告需要及时通知到玩家时使用。例如通知玩家服务器的资源世界将会重置。
2. **在公告板上发布公告** ，如服务器中的弹窗公告、QQ 群的群公告功能或服务器官网。  
此公告独立于聊天消息，玩家可以随时查阅，适合发布系统的说明时使用。例如发布服务器 tpa 功能的使用教程。

这两种方式并非必须二选一，你可以根据公告的性质决定是否要通过其中某种方式发布。有时候你有必要同时通过多种方式发布服务器公告。

## 示例

---

> 通过新的技术，我们成功恢复了机器人功能。但是出于本群性质，以及机器人帐号安全性的考虑，服务器内向群内发送聊天的功能引入了网易的屏蔽词列表。  
> 如果从服务器内向群内发送消息时未在群内显示，并且可排除机器人自身故障，证明你发送的消息中存在敏感或违规内容，不便在群内展示。  
> 从服务器向群内发送消息在消息前加上英文减号 (-) 即可，例如发送“-我挖到钻石了”。  
> 感谢你的支持与理解。今后也请多多支持《xxx 服务器》。  
> (改编自某玩家群)

---

> 🔞全凭自愿！  
> 如果你有能力且为成年人，并且想要捐赠💴我们，那么我们将不胜感激。  
> 请注意，服主与服务器提供者是不同的个体，但他们都致力于公益。以下是他们的分工：  
> Qingwmc：负责策划、处理玩家反馈、搭建维护服务器软件等  
> jpjl8845：负责为服务器赞助和维护硬件  
> (收款码图片)  
> (改编自某玩家群)  

---

> heibao114514 因为四处破坏信标被永久封禁，望各位不要违反服务器规定
> (改编自某玩家群)
