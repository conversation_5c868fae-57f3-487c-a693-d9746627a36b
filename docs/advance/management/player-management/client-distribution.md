---
title: 分发客户端
slug: /Sundry/Advance/management/player-management/client-distribution
sidebar_position: 6
---

# 分发客户端

当你的服务器要求玩家使用特定版本客户端或安装特定模组时，你需要主动为玩家提供客户端的获取方式，方便新玩家们更方便地玩上你的服务器。

## Java版

### JRE 的提供方式

:::tip

如果你不清楚此处提到的 JRE 是什么，请阅读 [选择、下载和安装 Java](/preparation/java/choose-and-download-and-install-java)

:::

虽然本教程的其他文档已经详细解释了 JRE 在 Minecraft Java 版游戏中的重要之处，但是各位服主的玩家并不都是对计算机有了解的玩家，他们可能并不清楚自己电脑上 Minecraft 是如何运行起来的。其中一些玩家可能在朋友或收费商家的远控下完成了 Java 环境的安装，甚至有些玩家一直在使用整合包提供的 JRE 。因此，对于一些服务器来说，提供一个傻瓜式的 JRE 安装向导或环境是很有必要的。

#### 在整合包中集成

如果你正在为玩家定制你服务器专用的客户端整合包，那么在你的整合包中集成 JRE 是方便且十分推荐的。

下面将以 HMCL 为例，分别说明如何在 Windows 版本的整合包中集成 JRE，同时也会介绍适用于其他启动器的通用方案。

1. **下载适合 Windows 的 JRE：**
    如果整合包对 JRE 没有特殊要求，你可以从 [Adoptium Temurin](https://adoptium.net/temurin/releases) 或 [Azul Zulu](https://www.azul.com/downloads/zulu/) 下载适合 `x64` 架构的压缩版（网站可能以“zip版”提供）JDK，因为这些 JDK 的体积较小，非常适合用于整合包使用。

    如果你如果你的整合包对 JRE 要求严格（例如必须使用Oracle JDK），你也可以通过以下通用方式找到压缩版发行包：

    * 打开该 JDK 厂商的官方网站；
    * 寻找与“Downloads”、“Releases”或“Archives”相关的页面；
    * 在下载页面中，选择平台为 `Windows`，架构为 `x64`（或 `amd64`），并查找文件名后缀为 `.zip`、`.7z` 或 `.tar.gz` 的版本；
    * 确认该版本为 **JDK 或 JRE**，并为你希望使用的 Java 版本（如 17、21）；
    * 下载后解压，即可将该文件夹整体打包进整合包中。

    通常情况下，压缩版 JDK 的路径结构会包含一个 `bin/javaw.exe`，只要设置启动器指向这个路径即可使用。如果找不到压缩包，可查阅其文档或使用社区维护的镜像站点（例如 Adoptium、MCL Mirror 等）寻找适配版本。

2. **解压至整合包目录，以 HMCL 为例，结构如下：**
   ```plain text
   MyModpack/
   ├── java/
   │   └── bin/
   ├── HMCL.exe
   ├── hmcl.json
   └── .minecraft/
   ```

3. **调整启动器设置：**
    以 HMCL 为例，前往 **版本管理** ，确保启用了 **启用版本特定游戏设置（不影响其他游戏版本）** ，**游戏Java** 选择 **指定Java版本** ，在此选项同一行的右侧输入框中填写以下内容：

    ```bash
    ./java/bin/javaw.exe
    ```

    :::tip

    如果你已经对路径格式有一定了解，可以根据实际情况调整整合包内集成的 Java 的位置，确保启动器中填写的 Java 路径能够正常定位到。

    :::

4. **保存后运行游戏测试：**
     游戏将不再依赖系统 Java，改为使用内置的 Java 运行。你可以在启动器的「设置」页面再次查看当前 Java 路径，确认它已指向你整合包中的 java/bin/javaw.exe。

5. **打包时建议附带启动器的配置文件** （例如 HMCL 的 `hmcl.json`，该文件在部分操作系统下是隐藏文件，默认情况下不调整文件管理器设置会看不到）以保留设置，便于玩家无需重复配置。
    对于 PCL2 或一些轻量或不完善的第三方启动器（包括一些魔改启动器），可以通过 **命令行启动脚本** 指定 JRE。

    例如使用一个批处理脚本 `start.bat` 启动 Minecraft：

    ```bat
    @echo off
    set JAVA_PATH=%~dp0jre\bin\javaw.exe
    %JAVA_PATH% -jar launcher.jar
    ```

    :::warning

    **整合包中集成 Java 的合规性风险**

    1. 部分 JDK 不允许再分发，请勿在你的整合包中集成这类 JDK 并公开发布（不包括仅上传至 QQ 群或提供官网或网盘下载），典型的禁止再分发的 JDK 就有 Oracle JDK。
    2. 如果你熟悉 Java 的文件结构并且希望根据你自身情况删除其中部分不需要的文件，那么请勿删除 **原始许可证文件和版权信息**。如果有特殊情况必须删除这些文件或者本身就已经丢失，那么请勿公开发布你的整合包。
    3. 如果整合包中由于包含了你的开发团队制作或你代理发售的付费软件（如付费模组）而需要玩家付费下载，那么请勿选用许可证中 **禁止进行收费或商业用途分发** 的 JDK 进行集成。

    :::

#### 提供 JDK 安装包的下载

让玩家和你一样自行在电脑上安装 Java。有条件的服主建议自制整合包并集成 Java，因为部分小白玩家计算机基础知识几乎为零，可能无法顺利地自行安装 Java。

首先，你可能需要自己下载一份你为玩家推荐的安装包。这么做的时候需要注意，为 Windows 玩家准备下载链接时请选择 `.msi` 或 `.exe` 后缀的安装器。

##### 直链下载

前往你为玩家推荐的 JDK 的官网发行版界面，找到下载按钮，右键下载按钮选择复制链接。通常情况下此时 JDK 的下载直链就已经位于你的剪贴板，随后将其发布至 QQ 群或官方等玩家公告中，玩家就可以点击链接就能直接在浏览器中开始 JDK 安装包的下载。

部分 JDK 官网下载可能没有固定下载直链，无法通过直接获取下载链接，此时可以找到第三方的 JDK 镜像站用上述方式获取直链。

##### 群文件

如果你的服务器有一个聊天群，而且对应平台支持在群内长期保存文件供群员下载（如 QQ 群），请利用好此功能，将 JDK 安装包上传至群文件，这样小白玩家在群里提问时，可以随时引导他们从群文件中下载。

##### 网盘下载

由于大多数 JDK 官方提供的下载服务器位于国外，一些玩家下载 JDK 时有概率遇到下载缓慢甚至反复下载都失败的问题。如果你有熟悉的其他社区贡献者提供的 JDK 网盘下载，可以将它们作为 JDK 下载的备用方式提供给玩家。你也可以使用自己的网盘帐号或服务器，上传 JDK 安装包并将其设置为公开下载。获取到网盘下载链接后，请在玩家公告或文档中将它和 JDK 官方下载链接放在一起。

避免选择 **要求安装客户端才能下载** 的，或 **对免费用户严格限速** 的网盘，这将大大降低新玩家的游戏体验，导致他们的新鲜感在下载安装 JDK 的过程中消散。此外，一些拥有极客精神的玩家也可能因为网盘的缘故选择离开。


##### 服务器官网下载

:::note

此方案仅推荐已经拥有大带宽可建站服务器，而且官网已经投入使用的服务器考虑。

:::

如果选择使用自己的网站服务器提供 JDK 的下载，你可以对玩家以直链下载或编写下载网页的方式提供。如果官方不是由你自己亲手搭建，建议与负责你服务器官网的运维进行沟通。

##### 提供官网下载页

难以找到直链时，再将官网下载页直接发送给玩家，并提供详细的版本选择教程。为了让玩家准确理解，请尽力手把手编写教程，告诉玩家应该依次点击哪个按钮。

**不推荐这种方式**，因为任何情况下都会有玩家错误地理解教程，下载到错误的 JDK，导致游戏无法启动，并将报错界面拍屏发到群里，造成不必要的麻烦。

#### 要求玩家自行寻找资源

**不推荐**，尤其是在模组服中，这将有可能导致许多新玩家在群里问问题。除非你的服务器已经决定只允许精英玩家加入，否则还是建议为普通玩家提供一种 JDK 的下载安装向导。

### 整合包下载

高版本或大型整合包的空间占用往往非常大，下载时也会消耗大量的流量，对提供下载服务的服务器有一定要求。

#### 第三方制作的整合包

为避免侵权纠纷，强烈推荐向玩家提供该整合包官方的下载方式（如原作者提供的 Curseforge、Github、网盘等）。如果普通玩家从官方渠道下载整合包大概率遇到困难（例如 Github 难以访问或某网盘对免费用户限速），请通过群文件等私下分享的形式分发整合包。

如果通过可能被视为再分发行为的方式提供（尤其是原作者已经提供网盘下载，而你又用自己的网盘帐号分享一遍，有时甚至不标明原作者），就可能给自己带来不必要的麻烦。如果必须通过此类方式提供，请先联系原作者取得授权。

#### 自行制作的整合包

##### 群文件（自制整合包）

**推荐**，通常建议优先通过群文件分享自行制作的整合包。

##### 网盘下载（自制整合包）

分享时，避免选择 **要求安装客户端才能下载** 的，或 **对免费用户严格限速** 的网盘。

##### 服务器官网下载（自制整合包）

搭建方式同[服务器官网下载](#服务器官网下载)

请注意，由于整合包体积非常大，建议只在网站服务器有无限流量、带宽较高、国内直连快速且稳定，或者已经配置了流量足够的 CDN 的前提下考虑。

##### 从启动器直接导出整合包甚至仅提供模组列表

**建议仅作为备用方式**，玩家需要自行处理所有资源查找和游戏安装，对于一位没有基础，只想玩服务器的小白来说这样的难度是十分劝退的，并导致大量的玩家在群里问问题，甚至造成新玩家流失率明显增加。

### 基岩版客户端下载

#### 安卓手机

##### 上传安装包至群文件

**最推荐**，几乎所有玩家都能够顺利下载到安装包。但是如果服务器有更新，需要管理员及时更新群文件中的安装包文件。

##### 引导玩家前往社区下载站下载 APK 安装包

目前以下网站可以被作为下载站直接提供给玩家：

- **MCBEDL**：https://mcbedl.com ，原为 mc.minebbs.com，MineBBS论坛提供的公益安装包下载站。
- **苦力怕论坛**：https://mcapks.net ，目前玩家群体中使用最多的下载站

请注意，务必提醒玩家 **“复制到浏览器打开”**。部分小白玩家可能会直接在 QQ 微信等软件中直接打开链接，导致无法下载文件，甚至网站无法正常显示。

#### Windows 电脑

通常情况下，Windows 玩家无需指导安装，这是因为正常情况下 Windows 版本的基岩版是必须付费购买（正版帐号）才能进行游戏的。然而如果有玩家提问“电脑能玩吗”，可以回答“能，但是要正版”。

需要注意的是，通常情况下，如果玩家不清楚电脑能不能玩基岩版，那么他可能没有正版，甚至对 Minecraft 国际版各个版本的区别都不了解。这种情况下，指导他在 Windows 上玩上基岩版需要花费大量的时间和精力。

如果你的服务器未支持最新基岩版客户端，那么玩家们使用从 Microsoft Store 中新下载的客户端将无法进入你的服务器。这种情况下，你需要为 Windows 玩家提供用于降级客户端的旧版安装包。与安卓和 iOS 不同的是，Windows 的 UWP 应用安装包 Appx 可以覆盖安装旧版，因此你只需要放心地指导玩家双击安装包并直接覆盖即可，他们的所有数据都会被妥善保留。

Windows 版本的安装包可以从 [MCBEDL](https://mcbedl.com) 或 [MCAPPX版本库](https://www.mcappx.com/) 下载。你自己下载完成后，可以通过与分发安卓安装包相同的方式将其分发给玩家。

#### iOS

iOS 由于系统限制，安装基岩版的门槛较高。目前没有低成本为 iOS 玩家提供安装方式的方案。

:::note

iOS 的基岩版存在限制，如果使用砸壳 ipa 自签安装，会导致无法登录微软帐号（登录失败，错误代码溺尸），这导致这种方法安装的基岩版是不能玩服务器的（原版基岩版不登录微软帐号无法连接包括离线服务器在内的任何服务器，而针对 iOS 的可进服务器的修改版 ipa 目前几乎没有）。

:::

##### 引导玩家白嫖第三方免费共享帐号

目前有活跃的微信公众号、论坛等会免费分享共享帐号，你可以引导玩家从这些地方下载 iOS 的基岩版。

使用共享帐号时，务必告知玩家不要在设置中登录共享帐号，否则会有锁机风险。

此方法 **无法安装旧版（降级）**，如果你的服务器未支持最新版基岩版进服，请参考 [Windows 版 iTunes 12.6.5 抓包降级](#windows-版-itunes-1265-抓包降级)

##### 自行维护共享帐号

**不推荐**，该方法需要自行注册外区 Apple ID、自行花钱购买礼品卡、兑换码等以购买 iOS 基岩正版，然后将自己的帐号分享给玩家。

其他一些组织分享的没有开启双重认证的共享帐号一般都是维护者自身就有渠道可以低成本重新制作帐号。如果自行维护共享帐号，务必开启双重认证，并频繁修改密码，否则帐号极易被封无法找回。

即使安全措施齐全，帐号也可能会被苹果官方判定为滥用并被永久封禁，冒着如此高的风险仅为分享 Minecraft 一个游戏的下载并不划算，因此不推荐。

此方法 **无法安装旧版（降级）**，如果你的服务器未支持最新版基岩版进服，请参考 [Windows 版 iTunes 12.6.5 抓包降级](#windows-版-itunes-1265-抓包降级)

##### 引导玩家安装巨魔商店等（即将失效）

巨魔商店暂不支持 iOS 16.7 和 iOS 17.1 及以上版本，而且其社区目前没有支持这些高版本的消息，网友也对该软件未来持悲观态度。如果需要使用该方法，可以自行搜索巨魔商店的安装教程。注意，巨魔商店在不同版本和不同机型上安装方式可能存在不同，搜索教程时请注意设备的机型和系统版本。

##### Windows 版 iTunes 12.6.5 抓包降级

:::note

此方法仅理论可以解释，目前实际操作成功案例极少，而且目前尚未有人在基岩版上做过测试。

:::

首先通过抓包降级的方式获取带有你自己 Apple ID 签名的 ipa，教程可以看 [Minecraft iOS基岩版怎么完美降级？（下）| 我的世界基岩版版本切换教学](https://b23.tv/BV1MN411k7p5)。此安装包可以直接在所有登录了你自己 Apple ID 的设备上安装，不会过期，无需签名，且安装好之后可以正常登录帐号，但目前尚不清楚直接在陌生人的设备上安装会有什么样的限制。

根据网友分享，随后将 ipa 通过爱思助手等软件直接执行签名操作，然后就可以安装在登录有对应帐号的设备上了。目前尚不知将这些安装包重新签名后是否会再次出现登录微软帐号时的“溺尸”错误。

### Java 版为 macOS/Linux/安卓/iOS 玩家提供支持

目前在服主圈子中，普遍在默认情况下假定所有使用这些操作系统的玩家均有一定计算机基础。你可以不在服务器文档或公告中提及这些操作系统下游戏的下载安装方式。由于 Java 本身跨平台，多数情况下玩家们根据他们已了解的基础知识对游戏的目录结构等进行简单的修改即可运行游戏并进入服务器。然而，有时玩家们会提出以下常见问题：

- 有些模组未在非 Windows 系统下经过充分测试，可能无法在非 Windows 系统下运行。
- macOS 下报错 `zip END header not found`：让玩家在游戏的 `mods` 目录下执行 `find . -name '._*' -type f -delete`
- 如果 macOS 玩家无法解压整合包，让他们从 AppStore 中下载安装 **MacZip** 或 **The Unarchiver** 进行解压
