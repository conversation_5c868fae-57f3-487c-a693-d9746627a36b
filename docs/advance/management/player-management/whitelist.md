---
title: 白名单制度
slug: /Sundry/Advance/management/player-management/whitelist
sidebar_position: 4
---

<!--markdownlint-disable no-duplicate-heading-->

# 白名单制度

为了保证服务器中所有的玩家都可信，你可能需要为服务器配置白名单。没有白名单的服务器将面临以下风险：

1. 同一个玩家使用多个帐号进服，同时领取多份限量奖励。
2. 不怀好意的陌生人在管理员和玩家不知情的情况下进入服务器并对其进行破坏，事后无法追究其责任。

:::warning

开启白名单的服务器将面临更大的管理压力，包括但不限于你需要长期在线来随时给新玩家添加白名单，并为离开服务器的玩家解绑白名单。  
如果你选择让玩家自助申请白名单，你或你的技术人员还需要学习、部署、配置和维护相关软件。

:::

<!--邮箱绑定白名单-->

## 机器人绑定白名单

让玩家根据指引与机器人交流来自助绑定白名单，

:::warning

非常建议使用拥有绑定白名单功能的机器人实现绑定，否则你可能需要自行开发软件或人工操作来解决 **退群玩家仍可进服**、**同玩家为多个帐号绑定白名单** 等一系列问题。

:::

### 准备内容

- 一个可以保证长期工作的群机器人

### 操作方法

以下机器人均已内置了玩家自助绑定白名单功能：

- [DLS](https://gitee.com/dlcn/dlscq/wikis/%E5%BC%80%E5%A7%8B%E4%BD%BF%E7%94%A8/5.%20%E9%85%8D%E7%BD%AE%E5%91%BD%E4%BB%A4%E5%92%8C%E6%AD%A3%E5%88%99#%E5%90%AF%E7%94%A8%E7%BB%91%E5%AE%9A%E7%99%BD%E5%90%8D%E5%8D%95%E5%91%BD%E4%BB%A4)：需要玩家交流群

## 人工添加白名单

除了使用机器人，你也可以手动完成添加白名单工作。由于人工操作繁琐，只建议在不便于搭建玩家自助绑定白名单功能时使用。

### 准备内容

- 保证长期在线随时响应玩家的管理员团队：你可以让服务器的客服团队来完成此工作。
- 一个用于记录玩家身份和 ID 对应关系的文档。

### 操作方法

1. **所有负责添加白名单的人都必须拥有使用 `whitelist` 指令的权限。** 你可以通过以下几种方式：
    - 为服务器机器人配置特定响应机制，机器人响应指定管理员的指定命令来执行`whitelist`命令
    - 在授予这些管理员游戏内管理员，这样他们就可以在游戏内使用 `whitelist` 指令了。BDS 服务器不支持在游戏内使用该命令，你可以为服务器安装插件或模组等来实现。注意这种方式可能需要管理员们启动游戏并进入服务器才能添加白名单，需要在游戏的启动和服务器连接过程上耗费大量时间。
    - 在支持多用户的[面板](../../../process/deploy/optional-mode.md#面板)上为这些管理员授予控制台的操作权限
    - 将服务器的后台交给这些管理员 (**不推荐** ，这会导致部分管理员获得其不需要的权限并产生安全隐患)

2. **为负责相关工作的管理团队培训** 。
    1. 为其安排值班时间保证管理团队在线时间尽可能长，以便及时响应玩家。
    2. 要求除重大事件外，优先响应新玩家添加白名单需求。
    3. 得知玩家游戏 ID 后，立刻在服务器上进行添加白名单操作。添加完成后，立刻通知玩家。最后才在白名单文档上记录玩家身份和 ID 的对应关系。
    4. 监控玩家退群、注销论坛等表明其离开玩家社区的操作，并立即移除玩家对应的白名单，并在玩家白名单文档上进行标记或删除操作。

:::warning 不要过于相信玩家！

**不要因为要求玩家在玩家交流平台上使用游戏 ID 作为昵称就不记录玩家身份与游戏 ID 的对应关系** 。一些玩家会趁管理员不注意将自己的昵称修改为他人的或无效的游戏 ID 来在事故追责中隐藏身份逃脱处罚。

:::
