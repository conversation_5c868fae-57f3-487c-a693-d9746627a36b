---
title: 进服审核
slug: /Sundry/Advance/management/player-management/audit
sidebar_position: 8
---

# 进服审核

对于一个服务器来说，维持良好的玩家社区是非常有必要的。社区的秩序不仅要由管理员监督，也要玩家共同来维护。然而玩家之间素质参差不齐，有些玩家就可以做到和其他人在服务器中合作，但是有些玩家就以破坏为乐，素质极差。为了筛选这些玩家，我们必须要知道以下种类的玩家会不利于玩家社区的发展：

1. **喜欢骂人，甚至以骂人为乐的玩家。**  
虽然有些玩家说话不文明，说话时嘴边总带点脏字，但是如果他不存在不尊重他人的情况，大多数玩家还是可以接受的。多数玩家无法接受的喜欢骂人的玩家通常会自私地维护他自己的观点，与别人观点发生分歧时首先辱骂他人而不是关注谁对谁错。有些玩家还喜欢无缘无故地给侮辱他人，比如给服务器内其他玩家起不恰当的外号、觉得某个玩家的建筑皮肤等不好看就对那个玩家进行羞辱等。
2. **熊孩子。**  
熊孩子通常是以在服务器中搞破坏为乐的人。他们喜欢拆家、偷盗、恶作剧等，这些行为严重影响了其他玩家的正常游戏。
3. **不明事理，不会为他人着想的人。**  
在日常生活中，为他人着想、换位思考是我们与他人互相尊重的非常重要的人格品质。在服务器中，有些玩家不知道如何尊重他人，在明知道自己错了却又强行辩解，“像个傻子一样”，让所有人都很尴尬。有时他们也会把随意拿走别人的东西、占有别人的家、借东西不还等行为看作理所应当，在其他人指出他错误时拒不道歉。这样的人非常讨厌，在服务器中也是一样。这些玩家还可能不尊重别人的劳动成果，别人一旦答应他使用某设施，或者同意给他游戏物资帮助他，他不但不说一声谢谢，还从此从别人那里索取，这就是典型的“伸手党”。

## 审核方式的分类

虽然我们常说小孩子才不懂事，但是现实生活中，这种乳臭未干的成年人也随处可见。如果你希望维护一个纯净、团结的社区，就得尽可能避免这些人进入服务器。进服审核是最有效、最通用的办法。玩家进服审核时通常有以下流程：

1. 玩家联系到审核通道。审核通道可以是一个群聊、一个人的联系方式、一个 mc 服务器或一个网站。
2. 玩家通过自助或人工的方式进行审核。
3. 如果程序或人工判定玩家审核通过，就将玩家的游戏名加入到服务器白名单，如果有玩家交流平台，那么就可以允许玩家加入交流平台。
这也意味着有进服审核机制的服务器必须开启白名单。否则玩家不经过审核也可以进入服务器，审核可能失去意义。

## 审核的形式

通常情况下，有以下几种审核的形式：

### 回答问题

你可以像学校考试那样准备好问题，然后让玩家回答。你可以使用一些软件的在线问卷收集功能来实现玩家在线答题。问卷设置为答卷者无法查看其他人的答案，然后你就可以把问卷发给玩家。当玩家填写好后，你可以进入问卷的统计界面获取玩家的答案并进行评分。

给玩家出的问题可以与你对玩家的要求相关。例如：

- **如果你希望玩家素质更高**，就可以出服务器内行为相关的题目，比如：
  - 当听说自己的好友偷了其他玩家的物品被抓时，你应该怎么做
  - 你正在使用的刷铁机的主人突然因为某些原因而禁止你使用他的刷铁机，你应该怎么做

- **如果你希望玩家的游戏水平更高**，你也可以出游戏知识相关的题目，比如：
  - 以下哪个红石电路可以实现 RS 锁存器的功能
  - 当一个不属于任何村庄的村民在距离附近的村庄至少多远处认领床时，才会创建新的村庄而不是加入那个附近的村庄

题目尽量不要过难，例如：

- **不能强行要求玩家答对涉及大量数学计算和计算机知识的题目**：会导致玩家由于题目太难，不再想加入服务器而离开
- **不要设置太多过于简单的题目**：会允许更多不符合要求的玩家进入服务器，导致审核失去意义。

### 面试

你可以要求玩家完成进行特定的任务。例如你可以让玩家在游戏中建造一个特定的建筑或生电机器、在有限的时间内达成某个游戏目标等。

### 考察

你可以先允许玩家进入服务器，然后对其进行重点关注，并对于该有玩家的所有违规行为都直接将其永久封禁，无视服务器现有处罚规则。

难度适中、知识面广的审核可以更好地筛选你认为适合玩家社区的人。在审核机制实行后，如果发现有玩家仍然不符合服务器玩家社区所需条件但通过了审核，或者有玩家明明符合条件却无法通过审核，你就需要对审核内容进行修改，调整太难或太简单的内容。
