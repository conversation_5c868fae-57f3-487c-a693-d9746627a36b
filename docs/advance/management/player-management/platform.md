---
title: 玩家交流平台
slug: /Sundry/Advance/management/player-management/platform
sidebar_position: 5
---

# 玩家交流平台

为了和玩家保持联系，也是让玩家之间能够互相联系，光有服务器中的聊天是远远不够的。服务器中的聊天功能少，无法保存聊天记录，在玩家下线之后也不再能及时联系到那个玩家。服务器需要在即时通信软件上建立交流平台，来扩充服务器社交的属性。与服务器内聊天相比，即时通信软件有以下优势：

1. 玩家身份唯一，不容易伪造身份。
2. 拥有保存聊天记录的功能，玩家们可以在即时通信软件上随时翻看自己和其他人的聊天记录，了解自己不在线时服务器中发生的事。
3. 即时通信软件可以随时向玩家发出提醒，让玩家与服务器社区保持紧密联系

目前使用最多的即时软件是 QQ，当然也有微信、Kook、Discord<!--、T＊＊＊＊＊＊＊-->等软件可以选择。

## QQ

在 QQ 上虽然有频道可以选择，但是笔者仍然非常建议使用 QQ 群。关于如何建 QQ 群的方法此处不做展开。建好 QQ 群后，你需要为 QQ 群进行以下设置：

### 设置群头像

群头像尽可能设置为服务器相关的图片。并且尽可能美观。

最推荐的是使用服务器的 logo 作为服务器群头像<!--，如果你还没有服务器 logo，可以按照后面营销章节学习设计 logo-->。如果你暂时不打算制作服务器 logo，也可以使用服务器中的照片作为群头像。在有条件的前提下，尽可能使用光影拍摄。

如果实在是没有可以作为服务器群头像的图片，你也可以找一张二次元的图片。

### 设置群名

群名一定要包含服务器的名字，如何为服务器起名字见营销章节。

比如你的服务器叫起源之地，那么就直接在服务器群名上写上“起源之地服务器交流群”即可。

此外，还建议对群名进行包装：

- 使用颜文字，例如“起源之地～(∠·ω< )⌒★”
- 加入服务器的英文名，例如起源之地“Land of Origin”
- 使用符号包装，例如“【LO】起源之地”
- 如果服务器与其他社交元素相关也可以加入社交元素名，例如“【起源之地】ブルーアーカイブ！”。

### 在群公告中发布服务器的连接地址和规则

为了让玩家能够知道服务器的进服方式，一般需要在群公告放上服务器的连接地址等。在写群公告时，需要注意以下事项：

1. 群公告需要打开置顶、发给新成员选项，尽可能不要打开“需成员确认收到”。
2. 群公告尽可能简短，过长的群公告不仅阅读起来费力，还可能会超过 QQ 群公告的字数上限。
3. 群公告尽可能语言简练，用词正式且礼貌。
4. 包含所有进服需要准备的内容，包含是否支持 Java 版/基岩版、服务器版本、是否需要安装指定客户端等。

<details>
    <summary>不好的群公告示例</summary>

> 8aka.org:33825  

没有写明游戏版本。

> Java 版服务器连接地址 8aka.org 端口 33825  

连接地址并没有直接写可复制粘贴到游戏内的格式，容易误导玩家。如果上述服务器支持互通，那么他并没有写上服务器支持互通。

> 服务器支持基岩版，不支持 Java 版  
> 连接地址：8aka.org  
> 端口：33825  

没有写明支持的游戏版本，如果此服务器不支持最新版，那么将导致大量新玩家无法立即进服，因为大部分玩家下载的基岩版都是保持最新版。

> Java 版连接地址：8aka.org:33825，版本 1.8-1.21  
> 基岩版连接地址：8aka.org，端口 33825，版本 1.21.20-21  
> 别管我问这问那的，几把爱进不进，进不去从自己身上找问题  

群公告包含不友善的用词。

</details>

<details>
    <summary>合格的公告示例</summary>

> 服务器仅支持基岩版  
> 连接地址 8aka.org 端口 33825  
> 版本支持 1.21.20-21  
> 如果进不去服务器请看其他置顶群公告，有解决方式，实在看不懂可以问群主  

---

> 欢迎来到起源之地服务器！
> ⚠️进服前请详细阅读本公告！⚠️
> Java 版进服地址：8aka.org:33825  
> 版本支持：1.8-1.21.1  
> 基岩版进服地址：8aka.org 端口：33825  
> 版本支持：1.21.0-1.21.21  
> 进入服务器前，请阅读服规：8aka.org/landoforigin  

---

> ⚠️服务器连接要求：  
> Java 版 1.21  
> 💻服务器 IP 地址：  
> mc.8aka.org  
> ⚠️注意事项：  
>
> 1. 服务器有多世界系统，玩家们可以通过主菜单中的维度传送服菜单进行维度传送；一般情况下，主世界维度和第二世界维度可用于给玩家们建造生存，且没有特殊情况不会换挡；资源世界维度用于给玩家们获得资源，每隔一段时间会重置一次，并且该维度所附属的地狱和末地与主世界维度不相通
> 2. 服务器安装有拓展玩法，需要在添加服务器时请将服务器资源包改为启用
> 3. 建议玩家们在加入服务器前安装 JEI(用于查看物品配方)、钠和锂 (优化客户端) 模组，也可以考虑添加部分玩家们在群文件中分享的一些适用于玩家们使用的模组整合包  
> (改编自“天空之城”玩家群)

---

> 暑假服已开启！  
> 插件生存服 (手机可进服，连接地址见基岩版)  
> 连接地址暂定为：  
> java 版 mc.8aka.org:19934  
> 基岩版 mc.8aka.org 端口 19934  
> 版本：游戏内容截至荒野更新 (1.19)，java 版推荐使用 1.19 及以上版本，最低支持 1.8，基岩版支持 1.21.0-2 和最新版  
> 即使使用基岩版 (手机版) 进服，游戏特性也和 java 版相同  
> 根据投票的结果，服务器暑假服的玩法定为插件生存服  
> 考虑到原版生存和生电玩法的票数同样很高，后续我们会积极准备这两种玩法，并在后续开设新服务器时考虑  
> 目前由于服务器刚刚起步，插件很少，各位玩家可以先开荒，插件将会陆续添加  
> 雾中人整合包模组服 (仅支持 windows，手机和 macos 无法运行整合包)  
> 连接地址 mc.8aka.org:38204  
> 模组整合包可在群文件的“国际服安装包”中找到  
> 雾中人是一款模组，主要以恐怖为主题，这个整合包还添加了一些其他的怪物和装备来丰富游戏内容  
> 由于此服务器消耗大量计算资源且热度较低，有时可能进入休眠，不会随时开放，如果无法连接服务器，暂时请主动提醒管理员开服，后续我们会开发相关软件让你能够在聊天群中自助唤醒服务器  
> (改编自“新月国际服”玩家群)

---

> 起源之地服务器 游玩规则
> 欢迎加入起源之地服务器，为了确保每位玩家都能享受愉快的游戏体验，请遵守以下基本原则：
>
> 1. 尊重他人：保持礼貌，不进行任何形式的骚扰或歧视。
> 2. 禁止作弊：不得使用作弊工具或利用游戏漏洞。
> 3. 保护财产：尊重他人劳动成果，不破坏或窃取他人物品。
> 4. 遵守 PVP 规则：在允许 PVP 的区域进行对战，避免恶意攻击。
> 5. 文明聊天：在公共聊天中保持文明，不发布不当言论。
> 6. 合理建造：在指定区域内建造，不侵犯他人领地。
> 7. 资源使用：合理使用资源，避免影响服务器性能。
> 8. 举报机制：发现违规行为，请及时向管理员举报。
> 9. 社区活动：积极参与服务器组织的活动，促进社区交流。
> 10. 特定模式规则：根据游戏模式 (生存、创造等) 遵守相应规则。
> 11. 赞助者特权：尊重赞助者，他们为服务器提供了支持。
> 12. 维护通知：关注服务器维护和更新通知，以免影响游戏。
> 违反规则将受到警告或封禁处理。我们期待与你共同营造一个和谐、有趣的游戏环境。
> 感谢你的理解与合作。  
> (改编自“天空之城”玩家群)

---

> 本群总规：
>
> 1. 严禁发布违法、涉政、恐怖、暴力、血腥、故意儒玛，闹紫砂以及其他令人不适的文字、图片、表情
> 2. 无论原因，严禁在群内发生任何形式的争吵
> 3. 本群允许进行轻度发癫和啬图，切记不能影响其他成员的正常聊天，图片不能过于擦边
> 4. 可以刷图刷屏 (包括戳一戳)，但是不能影响其他群友聊天，评判标准为其他群友正在讨论或开始其他话题，说“别刷了”不算
> 5. 严禁以任何行为恐吓、侮辱、排挤其他群员，禁止在群内透露他人个人隐私，如有发现直接按照群最高处罚制度处理
> 6. 开玩笑应当适度，若因小玩笑发生争吵，应当道歉或者私下以更好的方式解决
> 7. 头衔 (称号) 可以在群里艾特群主或者私信群主索要，每个艾特群主都会看到，但不一定会回复
> 8. 禁止任何形式的宣传，包括但不限于广告和宣群  
> 本群处罚制度：1 次十分钟，2 次一小时，3 次十二小时，4 次一天，5 次移出本群 (行为较严重的按*3 起步)
> 本规则即日起效，以往一概不论
> 以上规则解释权归群主所有

---

</details>

### 设置管理员

如果你有一整个团队开服，记得把其他服务器管理员设置成群管理员  

### 将群设置为公开

<!--进入群聊天界面，点击右上角的“三”，点击群号和二维码，-->

## 微信群

:::warning

由于微信群功能性质较为私密，不建议大型服务器采用

:::

:::info

微信群目前无法设置群头像、实现群机器人功能

:::

首先你需要已经有几位玩家或管理的好友，然后才能建群。

### [设置群名](#设置群名)

### [在群公告中发布服务器的连接地址和规则](#在群公告中发布服务器的连接地址和规则)

<!--## Kook

## Discord

## T＊＊＊＊＊＊＊

## 论坛网站-->

建好服务器交流群之后，你就可以拉人进群了。随后，你可以和玩家们直接在群里聊天，也可以随时通过艾特全体成员、发群公告的方式发布通知。  
