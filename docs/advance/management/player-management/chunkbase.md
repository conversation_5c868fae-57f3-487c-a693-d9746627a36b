---
title: Chunkbase
slug: /Sundry/Advance/management/player-management/chunkbase
sidebar_position: 2
---

# Chunkbase

Chunkbase 是 mc 玩家经常用的快速探索世界的网站，它提供一个工具，可以在已知世界种子的情况下快速定位到自己想要找的各种自然生成的建筑或者特殊区域，比如要塞、海底神殿、村庄、古城、下界要塞、猪灵堡垒、末地城等。

**Chunkbase**的网站是 chunkbase.com。

## 开始使用

要使用 Chunkbase 查找这些结构、生物群系等，你可以进入 Apps，会显示各种各样的工具。找到自己要查找的东西对应的工具，点击进入，输入种子并选择服务器核心的版本，然后就可以看到要找的东西的位置了。
要想缩放地图，可以在地图上转动滚轮，或拖动底部的滑块。在地图上拖动鼠标可以移动地图。如果要达到指定地点，可以在底部输入 x 和 z  坐标，然后点击**Go!**。

## Chunkbase 的利弊

Chunkbase 是一个非常好的工具，玩家可以用它快速地找到战利品所在地，而不需要跑图。对于服务器来说，由于玩家不需要跑图，为服务器节省了大量 CPU、网络和硬盘资源，但是同时由于它降低了玩家寻找战利品的难度，在某种程度上造成了玩家的活跃度降低，降低了服务器的热度，此外由于玩家可以快速找到附近所有的战利品，导致后来的玩家必须要跑很远才能获得战利品，一定程度上导致了游戏中不公平的现象。

## 如何对待 Chunkbase

你需要从一开服就对使用 Chunkbase 的玩家做好准备。你首先需要决定好是否为玩家提供使用 Chunkbase 的条件。你需要根据自己服务器的类型或玩家社区的个性来决定，通常来说有以下判断方式：

1. **玩家爱好** ：如果主要的玩家社区以生电为主，那么你很可能需要为他们提供 Chunkbase，如果玩家主要以战争、pvp 和探索为主，在提供 Chunkbase 时一定要谨慎。
2. **服务器配置** ：如果玩家有了 Chunkbase，他们就不再会不远万里地寻找特定的战利品，这会为你的服务器节省大量的 CPU、网络和硬盘。
如果你的服务器限制地图范围，那么提供 Chunkbase 将无关紧要，因为服务器内的资源本就有限，玩家跑图也跑不了太多，建议直接给玩家提供 Chunkbase，这样更方便了生电玩家。
3. **玩家数量** ：如果服务器的玩家较多，比如日常有十人在线，那么决定提供 Chunkbase 时一定要谨慎。如果你的服务器不限制跑图，那么服务器出生点附近的战利品将会被肝帝洗劫一空，后来的玩家就需要跑很远才能找到战利品。

## 如何提供或限制 Chunkbase 的使用条件

要想提供 Chunkbase 的使用条件，最简单的办法就是直接告诉玩家们地图的种子。在 Java 版中，地图种子普通玩家无法获取，互通插件 Geyser 也默认会隐藏种子 (显示 1)。对于 Java 版，你可以将种子号写在服务器公告上。基岩版默认即显示种子，只要你不安装假种子插件，玩家就可以自行查看种子。

要想不提供 Chunkbase 的使用条件，你可以隐藏种子。Java 版默认无法获取地图种子，所以你不需要进一步操作。对于基岩版，你需要安装假种子插件。
