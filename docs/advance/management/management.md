---
title: 经营管理
slug: /Sundry/Advance/management
sidebar_position: 6
---

# 前言

学习了开服的理论知识之后，各位服主还需要意识到一件事：开服不仅是一个技术活，它还需要自己的悉心管理和照料。mc 服务器就像一个网游，你不仅是游戏的后台管理员，还是游戏的美术、策划师、运营、项目经理等，或者你也可以选择招募团队，将不同的职位分配给不同的人。

随着相关软件生态的繁荣，有越来越多的新人进入 mc 服务器这个领域。有的人心怀远大理想，有的人觉得开服只是图一乐。至于抱有理想的人可以分为两类：一种是觉得现有服务器不能让自己满意的，一种是想在众人面前展示自己强大能力的。不过有一个事实：虽然开服对于一个已经对游戏已经有一定了解的人来说非常容易，但是对于很多计算机小白来说仍然是非常困难的，他们需要学会文件管理、使用正确的代码编辑器，能看懂英语或者会使用翻译软件，才能勉强运行服务器软件，更别说部署一个服务器了。这个事实导致大部分经验较少的人陷入了一个误区：因为有计算机知识的人和没什么计算机知识的人相比更了解计算机，这使得能顺利运行服务端的人产生了一种错觉：他们已经“高人一等”了，并认为这样就能把一个服务器逐渐开大。然而这样的想法是非常错误的，因为事实上 mc 服务器不止是保持一个软件开启这么简单，它的本质其实是一个网游，涉及到大量更深层次的计算机知识和营销手段，并不是一个人就能完全运营好的。在 b 站开服教程相关的地方我看到有人说：“如果想开一个大服务器，那么建议同时间至少有两到三个管理高强度在线”。

在服务器圈子待了这么长时间，我已经见过了无数服务器由于各种经营不善，长期没人在线甚至删库跑路的事。很多人疑惑不解，他们不知道为什么自己都已经开了服务器，也招了人，但是玩家就不来他们服务器，就去其他的服务器。他们根本不知道从哪里下手解决这个问题。于是服主们的远大理想纷纷泡汤，带着遗憾离开了。这也是 mc 服务器圈子一批一批的服主无视前人的警告前赴后继地跑来开服，遭受打击后又去继续警告后人，然后后人又不听劝的原因。大家对于开服开不起来的原因的认知多数都停留在“我不适合开服”，却不知道为什么自己开不好服。

:::tip

很多时候服务器开不好，都是因为服主**太善良**、**太天真**、**目标太不切实际**。

:::

## 要学习的内容

这一章你将不再学习工科方面的各软件操作方式，我们将进入文科和商科的领域，介绍各种经商之道、为人处事的方式。服务器在游戏中被称为“多人模式”，这证明必须要有其他人参与，你的服务器才能兴旺起来。与玩家和同行们更好地相处，并合理地安排服务器的花销，是你继续扩大服务器规模的关键。

本章包括以下内容：

### 玩家管理

与玩家和睦相处，并维持好玩家之间的关系是提高服务器玩家粘性，维持服务器热度的关键。

### 团队管理

很多时候，光凭你自己是无法胜任一个服务器的所有工作的。你需要招募团队，让大家各自负责擅长的领域，才能让服务器高效地运转起来。你需要学习如何作为一名合格的管理者，合理安排服务器工作人员，并让他们和谐相处，提高他们的工作效率。

### 宣传

为了提高服务器的知名度，招来更多玩家，合理的宣传是必不可少的。你需要学习如何最高效地宣传服务器，提高服务器的知名度。

### 策划

策划的目的就是让玩家玩到更丰富的内容，保证玩家在服务器内能够获得更多乐趣。合理的策划能迅速提高玩家对服务器的喜爱程度，而不恰当的策划则会导致玩家对服务器感到失望。

### 营销

对于一个优秀的 mc 服务器来说，营销是一大重点内容。你需要知道如何与玩家处理好关系才能提高玩家对你服务器的好感。你还需要知道如何才能在各个服务器的竞争中胜出。

### 资金管理

不要乱花钱。你需要知道如何安排服务器的支出才能让自己实现营收，避免破产。不要不重视资金的管理，如果管理不当，你的服务器随时可能会破产，甚至导致你背上债务。

## 要进行的准备

根据各开服教程，要开服只需要准备以下几样东西，《非常简单》：

- ~~云服务器或内网穿透软件帐号 + 闲置电脑~~
- ~~脑子~~

然而要开一个成规模的服务器，这些远远不够！况且有一些资源是你必须从开服就已经准备好的。

### 资金

资金对于开一个成规模的服务器来说是必不可少的，无论是维持服务器设备、软件和网络资源的运转，还是购买一些服务器需要的工具，钱都是最基本的，至于要用到多少钱，你可以用很少的钱巧妙地支撑起一个规模不小的服务器，也可以豪掷数万元直接开一个精美的大型服务器，但总之完全没有钱是不行的

### 脑子

不必多说，至少你需要能会独立思考，没有人会无偿手把手教你开服，而愿意花钱雇别人给自己处理技术问题的人也不会看这篇文章

### 时间

你有时间开服吗？很多人都希望开服其实就是把服务器开起来就可以放在那不管了，其实服务器运营所需要的时间比你想象的要多得多，并不是一个服务器的问题只需要几分钟就能解决，一些问题你可能从来没见过，你需要一点一点地搜索资料，排查问题，而这样一排查就是好几个小时，况且服务器一次可能不止出现一个问题，而且一个问题修完了就又出现另一个问题，这样一来就会耽误你大量的时间维护服务器，而且你不止需要处理服务器的技术问题，你还需要费心思给服务器进行宣传，对玩家进行管理，这些又要消耗大量的时间，而如果你不处理这些东西，服务器的情况就会急转直下，玩家需求得不到解决，玩家冲突没有人调解，服务器 bug 没有人修复，游戏体验最终烂成一坨，玩家纷纷离开。而如果你花费大量的时间来维护服务器，那么你的日常生活就会受到影响。对于一名初高中生来说，时间只会分为极其充裕和极其紧张两种：时间紧张的学生，时间安排都在学习或者未来事业相关，可能正在为中高考或者将来的职业努力，如果被 mc 服务器分散了时间，就很可能会对自己的前途造成影响，得不偿失；而时间充裕的学生当然可以给自己找点事干，来开 mc 服务器也没什么。对于一名大学生研究生来说，平时的课业是无论如何都要重视的 (如果你完全不担心毕业和工作那另说)，此外一些人可能因为自己的职业原因，比如必须要考英语四六级、计算机二级、考公、考驾照、考会计证、考教资这些，如果因为 mc 服务器耽误了时间，那么也是会对自己的前途造成很大影响，也是得不偿失。而对于已经工作的人来说，如果工作不忙那当然可以来开服，如果工作很忙，那开服一样地会耽误工作。很多时候你认为 mc 服务器是你自己的一个爱好，但是这个爱好是需要条件的，你需要投入大量的时间和精力到里面，你需要考虑 mc 服务器和自己生活是否冲突，并不是说开就开的。时间还和资金不一样，用不太多的钱一样可以开好服务器，**但是没有大把的时间是一定开不出好服务器的。**

### 游戏经验

你需要非常了解玩家都在在游戏里干出什么事来，比如玩家可能会利用这个特性刷这个东西，利用那个特性实现那个作弊，玩家之间游戏经验的差距，就会让游戏经验丰富的玩家在游戏中占据上风，而这种情况很可能会影响游戏平衡，破坏游戏体验。所以你需要非常清楚服务器里发生了什么，可能会发生什么，不能让玩家们肆意妄为

### 计算资源

你需要一台电脑来运行服务器软件，这台电脑可以是你自己的电脑，也可以是你购买的云服务器。对于一个没有人的服务器，只需要少量计算资源就可以维持运行，而对于一个有一定规模、经常有很多人在线的服务器，你需要大量的计算资源才能维持服务器运转，因为服务器要处理的玩家数据非常多。
计算资源这方面东西会在后面细讲，但是你知道需要有一定的计算资源。如果你要用自己的电脑，你需要保证你自己的电脑 24 小时开机，还需要保证它不会受停电或断网影响，也需要保证它不会影响你的日常生活，比如不能放在你自己卧室导致噪音造成你自己睡眠质量下降。至于云服务器则是虽然配置灵活但是想要一台又快又稳定的服务器也是价格不菲。如果你想要花很少的钱租云服务器，那么你的服务器一定会有经常宕机，甚至服务商跑路的风险。之前我就从别人手里低价接手了一年的一台海外服务器，结果才两个月服务商突然告诉我机房寄了，数据全丢，我去找他们要个说法，结果服务商直接重组，高层换人，根本没处说理去。

### 网络资源

很多服主刚入坑的时候还没有意识到一个严重的问题：由于 IPv4 公网地址稀缺，IPv6 又迟迟没有普及，在家用自己的电脑开服务器是很困难的。有的地区可以免费要到公网 IPv4 地址，有的地区要收费，有的地区完全不给。
公网 IPv4 可以说是开服必须的配置，如果家里没有公网，那么只能要么转向云服务器要么内网穿透。然而内网穿透也并不是很稳定，主流内网穿透运营商往往走低价路线，虽然价格实惠但是质量可能较差，而自己搭穿透节点往往需要找到符合自己需求的服务器，比云服务器和主流内网穿透的方案都要复杂。
解决了 IPv4 的问题之后，你还需要考虑服务器的域名，购买什么样的域名最好，是否要花更多的钱买更好的域名，都是需要考虑的。

### 耐心

需要耐心一共体现在两个方面：维护服务器和处理玩家反馈。维护服务器上文已经提到，你需要耗费好几个小时去找一个不一定能被找到而且找到了也不一定能修的 bug。至于玩家反馈，你需要耐心地解答玩家问题，即使是给他们发模板化的消息，也不要破防了对玩家们大喊大叫，那样只会把所有玩家都吓跑。
