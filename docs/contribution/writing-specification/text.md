---
title: 文本
sidebar_position: 2
---

# 文本

## 字间距

(1) 全角中文字符与半角英文字符之间，应有一个半角空格。

```text
错误：本文介绍如何快速启动 Windows 系统。

正确：本文介绍如何快速启动 Windows 系统。
```

(2) 全角中文字符与半角阿拉伯数字之间，有没有半角空格都可，但必须保证风格统一，不能两种风格混杂。

```text
正确：2011 年 5 月 15 日，我订购了 5 台笔记本电脑与 10 台平板电脑。

正确：2011 年 5 月 15 日，我订购了 5 台笔记本电脑与 10 台平板电脑。
```

半角的百分号，视同阿拉伯数字。

```text
正确：今年我国经济增长率是 6.5%。

正确：今年我国经济增长率是 6.5%。
```

(3) 英文单位若不翻译，单位前的阿拉伯数字与单位符号之间，应留出适当的空隙。

```text
例 1：一部容量为 16 GB 的智能手机

例 2:1 h = 60 min = 3，600 s
```

(4) 半角英文字符和半角阿拉伯数字，与全角标点符号之间不留空格。

```text
错误：他的电脑是 MacBook Air。

正确：他的电脑是 MacBook Air。
```

## 句子

(1) 避免使用长句。

不包含任何标点符号的单个句子，或者以逗号分隔的句子构件，长度尽量保持在 20 个字以内；20～29 个字的句子，可以接受；30～39 个字的句子，语义必须明确，才能接受；多于 40 个字的句子，任何情况下都不能接受。

```text
错误：本产品适用于从由一台服务器进行动作控制的单一节点结构到由多台服务器进行动作控制的并行处理程序结构等多种体系结构。

正确：本产品适用于多种体系结构。无论是由一台服务器 (单一节点结构)，还是由多台服务器 (并行处理结构) 进行动作控制，均可以使用本产品。
```

逗号分割的长句，总长度不应该超过 100 字或者正文的 3 行。

(2) 尽量使用简单句和并列句，避免使用复合句。

```text
并列句：他昨天生病了，没有参加会议。

复合句：那个昨天生病的人没有参加会议。
```

(3) 同样一个意思，尽量使用肯定句表达，不使用否定句表达。

```text
错误：请确认没有接通装置的电源。

正确：请确认装置的电源已关闭。
```

(4) 避免使用双重否定句。

```text
错误：没有删除权限的用户，不能删除此文件。

正确：用户必须拥有删除权限，才能删除此文件。
```

## 写作风格

(1) 尽量不使用被动语态，改为使用主动语态。

```text
错误：假如此软件尚未被安装，

正确：假如尚未安装这个软件，
```

(2) 不使用非正式的语言风格。

```text
错误：Lady Gaga 的演唱会真是酷毙了，从没看过这么给力的表演！！！

正确：无法参加本次活动，我深感遗憾。
```

(3) 不使用冷僻、生造或者文言文的词语，而要使用现代汉语的常用表达方式。

```text
错误：这是唯二的快速启动的方法。

正确：这是仅有的两种快速启动的方法。
```

(4) 用对“的”、“地”、“得”。

```text
她露出了开心的笑容。
(形容词＋的＋名词)

她开心地笑了。
(副词＋地＋动词)

她笑得很开心。
(动词＋得＋副词)
```

(5) 使用代词时 (比如“其”、“该”、“此”、“这”等词)，必须明确指代的内容，保证只有一个含义。

```text
错误：从管理系统可以监视中继系统和受其直接控制的分配系统。

正确：从管理系统可以监视两个系统：中继系统和受中继系统直接控制的分配系统。
```

(6) 名词前不要使用过多的形容词。

```text
错误：此设备的使用必须在接受过本公司举办的正式的设备培训的技师的指导下进行。

正确：此设备必须在技师的指导下使用，且指导技师必须接受过由本公司举办的正式设备培训。
```

## 英文处理

(1) 英文原文如果使用了复数形式，翻译成中文时，应该将其还原为单数形式。

```text
英文：...information stored in random access memory (RAMs)...

中文：⋯⋯存储在随机存取存储器 (RAM) 里的信息⋯⋯
```

(2) 外文缩写可以使用半角圆点 (`.`) 表示缩写。

```text
U.S.A.
Apple， Inc.
```

(3) 表示中文时，英文省略号 (`...`) 应改为中文省略号 (`⋯⋯`)。

```text
英文：5 minutes later...

中文：5 分钟过去了⋯⋯
```

(4) 英文书名或电影名改用中文表达时，双引号应改为书名号。

```text
英文：He published an article entitled "The Future of the Aviation".

中文：他发表了一篇名为《航空业的未来》的文章。
```

(5) 第一次出现英文词汇时，在括号中给出中文标注。此后再次出现时，直接使用英文缩写即可。

```text
IOC(International Olympic Committee，国际奥林匹克委员会)。这样定义后，便可以直接使用“IOC”了。
```

(6) 专有名词中每个词第一个字母均应大写，非专有名词则不需要大写。

```text
“American Association of Physicists in Medicine”(美国医学物理学家协会) 是专有名词，需要大写。

“online transaction processing”(在线事务处理) 不是专有名词，不应大写。
```

## 名词

### 专有名词使用正确的大小写

大小写相关用法原属于英文书写范畴，不属于本 Wiki 讨论内容，在这里只对部分易错用法进行简述。

正确：

```text
使用 GitHub 登录
我们的客户有 GitHub、Foursquare、Microsoft Corporation、Google、Facebook，Inc.。
```

错误：

```text
使用 github 登录
使用 GITHUB 登录
使用 GitHub 登录
使用 gitHub 登录
使用 g ｲんĤЦ8 登录
我们的客户有 github、foursquare、microsoft corporation、google、facebook，inc.。
我们的客户有 GITHUB、FOURSQUARE、MICROSOFT CORPORATION、GOOGLE、FACEBOOK，INC.。
我们的客户有 GitHub、FourSquare、MicroSoft Corporation、Google、FaceBook，Inc.。
我们的客户有 gitHub、fourSquare、microSoft Corporation、google、faceBook，Inc.。
我们的客户有 g ｲんĤЦ8、ｷ ouЯƧqu ﾑгє、๓เςг๏ร๏Ŧt ς๏гק๏гคtเ๏ภn、900913、ƒ4 ᄃëв๏๏к，IПᄃ.。
```

注意：当网页中需要配合整体视觉风格而出现全部大写／小写的情形，HTML 中请使用标淮的大小写规范进行书写；并通过 text-transform: uppercase;／text-transform: lowercase; 对表现形式进行定义。

### 不要使用不地道的缩写

正确：

```text
我们需要一位熟悉 JavaScript、HTML5，至少理解一种框架 (如 Backbone.js、AngularJS、React 等) 的前端开发者。
```

错误：

```text
我们需要一位熟悉 Js、h5，至少理解一种框架 (如 backbone、angular、RJS 等) 的 FED。
```
