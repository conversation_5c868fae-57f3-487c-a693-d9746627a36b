---
title: 段落
sidebar_position: 3
---

# 段落

## 原则

- 一个段落只能有一个主题，或一个中心句子。
- 段落的中心句子放在段首，对全段内容进行概述。后面陈述的句子为中心句子服务。
- 一个段落的长度不能超过七行，最佳段落长度小于等于四行。
- 段落的句子语气要使用陈述和肯定语气，避免使用感叹语气。
- 段落之间使用一个空行隔开。
- 段落开头不要留出空白字符。

## 引用

引用第三方内容时，应注明出处。

```text
One man’s constant is another man’s variable. — <PERSON>
```

如果是全篇转载，请在全文开头显著位置注明作者和出处，并链接至原文。

```text
本文转载自 WikiQuote
```

使用外部图片时，必须在图片下方或文末标明来源。

```text
本文部分图片来自 Wikipedia
```

## 插件 info

如无特殊情况，在插件的 info 中遵循这样的顺序

:::info

`官网` https://xxx

`Bukkit` https://dev.bukkit.org/projects

`SpigotMC` https://www.spigotmc.org

`Hangar` https://hangar.papermc.io

`Modrinth` https://modrinth.com

`MineBBS` https://www.minebbs.com

`GitHub` https://github.com

`文档(英文)` https://xxx

`文档(中文)` https://xxx

`插件百科` https://mineplugin.org

:::

如果插件没有在某网站有信息，跳过不写那一行

如果文档只有一个，不需要标注是中文还是英文

明确**大小写**，完全按照此页中的大小写样式
