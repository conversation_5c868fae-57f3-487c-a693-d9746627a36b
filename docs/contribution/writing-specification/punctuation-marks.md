---
title: 标点符号
sidebar_position: 5
---

# 标点符号

## 原则

(1) 中文语句的标点符号，均应该采取全角符号，这样可以与全角文字保持视觉的一致。

(2) 如果整句为英文，则该句使用英文/半角标点。

(3) 句号、问号、叹号、逗号、顿号、分号和冒号不得出现在一行之首。

(4) 点号 (句号、逗号、顿号、分号、冒号) 不得出现在标题的末尾，而标号 (引号、括号、破折号、省略号、书名号、着重号、间隔号、叹号、问号) 可以。

## 句号

(1) 中文语句的结尾处应该用全角句号 (`。`)。

(2) 句子末尾用括号加注时，句号应在括号之外。

```text
错误：关于文件的输出，请参照第 1.3 节 (见第 26 页。)

正确：关于文件的输出，请参照第 1.3 节 (见第 26 页)。
```

## 逗号

(1) 逗号 (`，`) 表示句子内部的一般性停顿。

(2) 注意避免“一逗到底”，即整个段落除了结尾，全部停顿都使用逗号。

## 顿号

(1) 句子内部的并列词，应该用全角顿号 (`、`) 分隔，而不用逗号，即使并列词是英语也是如此。

```text
错误：我最欣赏的科技公司有 Google，Facebook，腾讯，阿里和百度等。

正确：我最欣赏的科技公司有 Google、Facebook、腾讯、阿里和百度等。
```

(2) 英文句子中，并列词语之间使用半角逗号 (`,`) 分隔。

```text
例句：Microsoft Office includes Word, Excel, PowerPoint, Outlook and other components.
```

(3) 中文句子内部的并列词，最后一个尽量使用 (`和`) 来连接，使句子读起来更加连贯，下面两个句子都可以，第二个更优。

```text
正确：我最欣赏的科技公司有 Google、Facebook、腾讯、阿里，以及百度等。

正确：我最欣赏的科技公司有 Google、Facebook、腾讯、阿里和百度等。
```

## 分号

(1) 分号 (`；`) 表示复句内部并列分句之间的停顿。

## 引号

(1) 引用时，应该使用全角双引号 (`“ ”`)，注意前后双引号不同。

```text
例句：许多人都认为客户服务的核心是“友好”和“专业”。
```

(2) 引号里面还要用引号时，外面一层用双引号，里面一层用单引号 (`‘ ’`)，注意前后单引号不同。

```text
例句：鲍勃解释道：“我要放音乐，可萨利说，‘不行！’。”
```

## 括号

(1) 补充说明时，使用全角圆括号 (`()`)，括号前后不加空格。

```text
例句：请确认所有的连接 (电缆和接插件) 均安装牢固。
```

(2) 几种括号的中英文名称。

|       |             英文            |   中文 |
|-------|:---------------------------:|:-------:|
| `{ }` | braces 或 curly brackets    | 大括号 |
| `[ ]` | square brackets 或 brackets | 方括号 |
| `< >` | angled brackets             | 尖括号 |
| `( )` | parentheses                 | 圆括号 |

## 冒号

(1) 全角冒号 (`：`) 常用在需要解释的词语后边，引出解释和说明。

```text
例句：请确认以下几项内容：时间、地点、活动名称和来宾数量。
```

(2) 表示时间时，应使用半角冒号 (`:`)。

```text
例句：早上 8:00
```

## 省略号

(1) 省略号 (`⋯⋯`) 表示语句未完、或者语气的不连续。

(2) 省略号占两个汉字空间、包含六个省略点，不要使用`。。。`或`...`等非标准形式。

(3) 省略号不应与“等”这个词一起使用。

```text
错误：我们为会餐准备了香蕉、苹果、梨…等各色水果。

正确：我们为会餐准备了各色水果，有香蕉、苹果、梨⋯⋯

正确：我们为会餐准备了香蕉、苹果、梨等各色水果。
```

## 感叹号

(1) 应该使用平静的语气叙述，尽量避免使用感叹号 (`！`)。

(2) 不得多个感叹号连用，比如`！！`和`!!!`。

## 破折号

(1) 破折号`————`一般用于进一步解释。

(2) 破折号应占两个汉字的位置。如果破折号本身只占一个汉字的位置，那么前后应该留出一个半角空格。

```text
例句：直觉————尽管它并不总是可靠的————告诉我，这事可能出了些问题。

例句：直觉 —— 尽管它并不总是可靠的 —— 告诉我，这事可能出了些问题。
```

## 连接号

(1) 连接号用于连接两个类似的词。

(2) 以下场合应该使用直线连接号 (`-`)，占一个半角字符的位置。

- 两个名词的复合
- 图表编号

```text
例句：氧化 - 还原反应

例句：图 1-1
```

(3) 数值范围 (例如日期、时间或数字) 应该使用波浪连接号 (`～`) 或一字号 (`—`)，占一个全角字符的位置。

```text
例句：2009 年～2011 年
```

注意，波浪连接号前后两个值都建议加上单位。

(4) 波浪连接号也可以用汉字“至”代替。

```text
例句：周围温度：-20 °C 至 -10 °C
```
