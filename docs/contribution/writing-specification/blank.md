---
title: 空格
sidebar_position: 6
---

# 空格

## 链接之间增加空格

正确：

```text
请提交一个 [Issue](链接) 并分配给相关同事。
访问我们网站的最新动态，请 点击这里 进行订阅！
```

错误：

```text
请提交一个[Issues](链接)并分配给相关同事。
访问我们网站的最新动态，请点击这里进行订阅！
```

## 加粗、斜体、高亮文本前后加空格

建议在 加粗、斜体、高亮文本 前后加空格，否则某种情况会出现格式解析失败。

正确：

```text
修复了一个 **内存泄露** 问题，该问题由 someone 在 版本 v0.1.1 中引入。
**测试文本**，这是测试。
```

错误：

```text
修复了一个**内存泄露**问题，该问题由 someone 在版本 v0.1.1 中引入。
**测试文本** ，这是测试。
```

## 每行结尾不要空格

正确：

```text
怎样解决苦难？
只要活着，我们每一个人，都会面临苦难。
```

错误：

```text
怎样解决苦难？   
只要活着，我们每一个人，都会面临苦难。   
```

可以在 VSCode `查看 > 外观 > 显示空格` 处启用空格高亮的功能。

## 空行

- 不要有多余的空行
在 Markdown 文本中，想要做到渲染后 真换行 通常是使用两个空格加一个回车换行符 (Unix 下只有回车 CR)，或者粗暴地空一行，但是 请不要连续空两行及以上。
- 文件末尾空一行
强烈建议文件末尾空一行，大多数格式检查工具都会检查文件末尾的空行。文件末尾增加空行的可能原因是为了方便进行文件拼接处理。
- 标题前后各空一行
