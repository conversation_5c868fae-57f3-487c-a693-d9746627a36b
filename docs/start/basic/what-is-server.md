---
title: 什么是服务端？
sidebar_position: 1
---

## 什么是服务端？

> 服务端是一种有针对性的服务程序。服务端是为客户端服务的，服务的内容诸如向客户端提供资源，保存客户端数据。

简单来说，我们通常通过 *Minecraft* **启动器**玩的游戏属于 **客户端**。

当我们进行 *多人游戏* 联机时，我们需要连接到一个服务器，当玩家们在同一个服务器环境下，我们才能实现联机。

而这个允许我们进行联机的 **软件/程序** 可以简单称为 **服务端**。

## 为什么需要服务端

*Minecraft* 本身是开放联机的，也就是说玩家可以在 `ESC 菜单中` 将自己的客户端设置为 `"对局域网开放"`

我们就可以实现联机了，那么服务端的作用是什么呢？我们可以想象，如果选择 `"对局域网开放"` 的客户端作为服务端时，

如果出现该玩家想要退出客户端，那么所有人的链接都会中断。因此，为了更加稳定的联机，

*Minecraft* 提供了让玩家可以进行 *多人游戏* 的服务端，称为 **Minecraft Server**，俗称 **Vanilla Server** 、**香草端**、**原版端**。

:::tip

需要注意的是，`Vanilla` 本身的意思有“香草 (的)”、“原版”、“基本的”在计算机领域也代指“**原版**”。

:::

## 原版服务端的延伸

由于原版 **Minecraft Server** 拓展性及性能不足，社区衍生出了多种提供 *多人游戏* 的 **服务端**，

这些提供 *多人游戏* 服务的 **程序/软件** 常被人称为 **核心**，大致可分为
**[Mod](https://nitwikit.8aka.org/Java/start/basic/what-is-mod)** 服务端和 **[插件](what-is-plugin.md)** 服务端。

常见的 **Mod** 服务端有 **Forge** 和 **Fabric** 等。

常见的 **插件** 服务端有 **Bukkit** 和 **Paper** 等。

## 一些误区

服务器不能在已经关闭或睡眠的电脑上运行，~~人类的科技还没有办法做到虚空生电~~
