---
title: 什么是权限？
sidebar_position: 5
---

# 什么是权限？

权限 - 在插件服务端中，确认玩家做某行为的能力是否被允许。

节点 - 权限的单元，一个节点就是一条权限，可以使用权限系统插件给予各个用户，如 `cmi.tp` 就是一条权限节点，而权限节点前的 `cmi` 就是父节点。

通配符 - 即为 `*` ，当给予玩家权限 `父节点.*` 即可获取父节点对应的所有权限节点，如 `cmi.*` 即代表 所有的 `cmi.kit` `cmi.heal`...等节点。

继承 - 继承就是子用户组可以使用从父用户组继承的各种属性。

## 关于权限

***合法而稳定的权力在使用得当时很少遇到抵抗。 ——塞·约翰逊***

***最大限度地行使权力总是令人反感；权力不易确定之处始终存在着危险。--塞·约翰逊***

## 权限的作用

总的说来，权限就是可以让服主和管理组能有效地管理用户的权限，能够准确地控制一个玩家能干什么、不能干什么。

## 权限的设置

我们有且只推荐 LuckPerms 作为权限插件，具体的使用方法请参考 [LuckPerms 使用方法](https://nitwikit.8aka.org/Java/permission)

以下举例了一些常见的权限组分类：

| 组名          | 说明                                              |
|:------------|:------------------------------------------------|
| Newbie(新人)  | Newbie 用户组包括最基础的权限节点，应该给予所有新加入玩家在注册时保证安全的权限     |
| Player(玩家)  | Player 用户组包括了所有玩家可以使用的权限。一般包含经济、基础传送，和其他的基础指令   |
| Helper(协管员) | 一般允许拥有踢出玩家、监禁玩家、封禁玩家 IP 等权限。                    |
| Admin(管理员)  | 一般允许使用所有权限，且通常是唯一一个拥有控制插件、规定用户组、关闭/重启服务器命令的用户组。 |

当然你也可以自定义一个例如 VIP1、VIP2 的权限组，拥有比普通用户更多的权限。
