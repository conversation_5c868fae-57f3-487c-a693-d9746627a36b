---
title: NukkitX 核心介绍
sidebar_position: 1
---

![NukkitXLogo](https://www.minebbs.com/attachments/banner-png.21695/)

## 什么是 NukkitX

NukkitX（现通称 Nukkit）是由 CloudburstMC 团队维护的 Nukkit 分支版本，是被原 Nukkit 团队官方认可的继承者。
NukkitX 的 GitHub 仓库首次提交时间不详，但其作为 Nukkit 的继承者，继承了 Nukkit 的特性，并继续进行维护和更新。

NukkitX 的开发团队 CloudburstMC 专注于维护和改进 NukkitX，但不涉及对新特性的开发。
## 特色

- **高性能与稳定性**：NukkitX 使用 Java 编写，运行速度更快且更加稳定。
- **良好的插件生态**：NukkitX 拥有友好的结构，开发者可以轻松参与开发，并将其他平台的插件移植到 NukkitX。
- **多版本支持**：NukkitX 几乎支持所有 Minecraft 基岩版的版本。
- **持续更新**：CloudburstMC 团队积极维护 NukkitX，不断更新以支持新的游戏版本和修复问题。

## 链接
- [Github 链接](https://github.com/CloudburstMC/Nukkit)