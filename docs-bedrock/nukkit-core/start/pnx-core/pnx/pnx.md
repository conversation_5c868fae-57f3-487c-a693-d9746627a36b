---
title: PowerNukkitX 核心介绍
sidebar_position: 1
---

![PowerNukkitXLogo](https://www.minebbs.com/attachments/pnx_banner-png.25436/)

## 什么是 PowerNukkitX

PowerNukkitX（简称 PNX）是由包括 BlocklyNukkit 团队在内的中国 Nukkit 开发者们负责开发维护的 Nukkit 分支版本，
修复了众多 BUG，改善了性能，及时跟进新版本支持、支持 1.19 所有新方块和新物品、支持新版下界、支持史诗地形生成器 (Terra)。
在这些大更改的情况下，PNX 仍然保持了良好的插件兼容性，Nukkit 生态中的数千个插件仍然可以在 PNX 上运行。

得益于完全开源的代码，您在遵守开源协议的情况下使用 PowerNukkitX 没有任何法律风险，
内部的数十万 API 可以随意调用，您可以深入查看实现，
通过 JVM 生态中的各种工具及其方便地进行开发、调试、注入、拦截。也正因如此，
Nukkit 生态丰富多彩，成百上千各种各样的插件供您选择，您可以用您喜欢的任意 JVM 语言编写插件，如 Java,Kotlin,Scala…… ，同时也支持使用 Javascript 编写插件。

## 特色

- 支持基岩版 1.19.50 协议。
- 原生支持 384 限高（仅主世界）。
- 原生支持地狱世界，无需另外安装插件补丁等。
- 支持 3D 生物群系（完善中）。
- 原生支持原版命令和命令方块等（完善中）。
- 内置 Terra 地形生成器（如有问题点此查看）。
- 支持使用 JavaSrcipt 语言编写插件（初步完成，开发文档在此处）。
- 支持自定义方块 / 物品 / 实体（完善中，文档待补充）。
- 内置生物 AI，无需安装 MobPlugin（开发中，未完善）。

## 链接
- [Github 链接](https://github.com/PowerNukkitX/PowerNukkitX)