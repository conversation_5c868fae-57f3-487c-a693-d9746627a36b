---
title: PM1E 争议问题
sidebar_position: 1
---

:::warning
Nukkit PetteriM1 Edition 是一个独立开发的开源项目，由 PetteriM1 及其团队负责维护和更新

该项目任何争议事件与 NitWiki 站点以及任何文档维护者没有任何关联

NitWiki 仅提供关于 Nukkit PetteriM1 Edition 的信息和教程，但不对该项目的开发、维护或任何相关争议负责

所有关于 Nukkit PetteriM1 Edition 的开发、更新和维护均由 PetteriM1 及其团队独立完成，与 NitWiki 站点和文档维护者无关
:::

:::tip
本事件自己去看，我们不会录入：刀哥《[为什么你不应该使用 PM1ENK - 细说 PM1ENK 的罪状](https://www.minebbs.com/threads/pm1enk-pm1enk.16912/)》

还有：NukkitPetteriM1Edition 不知道啥时候开始老老实实把源代码推回来了：

![?](./zhengyi.png)
:::

大致事件如下：

2022 年 11 月 15 日，PetteriM1 删除了 PM1E 仓库下的所有代码，并表示接下来只会发布混淆过的 jar 文件供使用

此举引起了社区的强烈不满，据知情人士透露，这么做的原因是为了防止“别人抄袭他的代码”

而在闭源后，PM1ENK 又被指控在接下来的更新中存在对 PowerNukkitX 的抄袭

在此期间，一位名为 sora-kawaii 的 Github 用户成功反混淆了 PM1ENK 并公布了源码，公开的代码显示了 PM1ENK 对开源项目的抄袭

2023 年 1 月 18 日，PM1ENK 发布了更新，此次更新被认为使用了业内最贵的付费混淆器 ZKM 以加强混淆效果

这一违背开源协议的行为立刻掀起了社区中抵制 PM1ENK 的浪潮，随后社区推出了基于 PM1ENK 最后一个开源版本开发的 Nukkit-MOT 核心
