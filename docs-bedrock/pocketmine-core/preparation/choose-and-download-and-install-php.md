---
title: 选择、下载和安装 PHP
sidebar_position: 1
---

# 选择、下载和安装 PHP

:::tip
PHP 安装等待有缘人进行补充修改......

ps：因为 PHP 官网下载的 PHP 环境是纯净版，没有任何依赖，但是大多数 PHP 环境是需要部分依赖

考虑到弄这些依赖极其麻烦，因此参考了 PMMP 的安装文档，推荐使用 PMMP 打包的 PHP 环境系统

另外因为编写者没有用过 PMMP（雾），很多 PHP 安装情况并不清楚

如果你是 PMMP 使用者并且愿意为我们提供更多关于 PMMP 的信息支持，请在 NitWiki 仓库中提 PR 或者 QQ 群联系

如果你真的需要安装 PMMP，请参考官方安装文档：[PMMP 安装文档](https://doc.pmmp.io/en/rtfd/installation.html)
:::

## 安装环境

推荐 PMMP 专门打包 PHP 的 Github 仓库下载对应系统的 PHP 文件：[Github Releases](https://github.com/pmmp/PHP-Binaries/releases)



