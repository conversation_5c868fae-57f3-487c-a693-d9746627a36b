---
title: 可能用到的网站
sidebar_position: 1
---

# 可能用到的网站

开服不能只是从某一网站或者简单百度、Bing、谷歌获取资源，这样获取的资源种类非常贫乏。

以下是一些可供参考的推荐网站：

## MineBBS

国内较为知名的综合性 Minecraft 论坛。主要以基岩版开服内容为主，MCBBS 关闭后其 Java 版相关内容丰富了许多。

<details>
  <summary>点击展开</summary>

  <div style={{textAlign: 'center'}}>
    <a href="https://www.minebbs.com/">
      <img
        src="https://www.minebbs.com/data/assets/logo/mb-logo-blue-1x.png"
        style={{width: '100px'}}
        alt="MineBBS"
      /><br /><b>MineBBS</b>
    </a>
  </div>

插件板块：https://www.minebbs.com/resources/categories/bdserver.38/

同时，本教程在 MineBBS 也有搬运贴：https://www.minebbs.com/threads/nitwikit-geyser.26356/

</details>

## Bedrinth

由 LeviLDev 创建的专门收集 LeviLamina 和 EndStone 模组或者插件的资源网站。

<details>
  <summary>点击展开</summary>

  <div style={{textAlign: 'center'}}>
    <a href="https://bedrinth.com/">
      <img
        src="https://futrime.github.io/lip/images/logo.webp"
        style={{width: '100px'}}
        alt="Bedrinth"
      /><br /><b>Bedrinth</b>
    </a>
  </div>

插件板块：https://bedrinth.com/

</details>

## GitHub

全球最大的代码托管平台 **_建议注册账号_**。

<details>
  <summary>点击展开</summary>

  <div style={{textAlign: 'center'}}>
    <a href="https://github.com">
      <img
        src="https://logos-world.net/wp-content/uploads/2020/11/GitHub-Symbol.png"
        style={{width: '100px'}}
        alt="GitHub"
      /><br /><b>GitHub</b>
    </a>
  </div>

全球最大的社交编程及代码托管网站。

许多开发者会把自己编写的插件发到 GitHub。

虽然不登录账号不影响你浏览仓库和下载 Release 等。

但是登录后可以给作者发 Issues 来报告问题，提交新需求/建议，还可以下载 Actions 中的文件。

<details>
  <summary>注册问题</summary>

[在 GitHub 上创建帐户](https://docs.github.com/zh/get-started/start-your-journey/creating-an-account-on-github)

</details>

<details>
  <summary>连不上怎么办</summary>

这是由于 GitHub 是开放的外国网站，网站上时不时会有一些不能在此讨论的内容，所以运营商会刻意地屏蔽这个网站，在很多时候都不能正常访问。具体的表现如下：[只要 Github 域名指向任意 IP，该 IP 的 443 端口就会超时 3 分钟](https://blog.csdn.net/weixin_43659597/article/details/*********)。

有以下几种解决办法：

1. 魔法
2. [改 hosts](https://www.cnblogs.com/eudaimonia/p/********.html#**********)
3. [Watt Toolkit](https://steampp.net/)：下载安装完成后，在左侧侧边栏切换到**网络加速**，点击**平台加速 (免费)**，往下翻勾上 GitHub，然后点击**一键加速**，随后就可以正常访问 GitHub 了
<!--[点击此处](https://cn.bing.com/search?q=%E8%BF%9E%E4%B8%8D%E4%B8%8AGitHub%E6%80%8E%E4%B9%88%E5%8A%9E)-->

</details>

<details>
  <summary>下载太慢怎么办</summary>

使用魔法或者用加速地址：

https://gitmirror.com/files.html

https://moeyy.cn/gh-proxy

https://ghps.cc/

</details>

GitHub 汉化插件：https://github.com/maboloshi/github-chinese

地址：https://github.com

</details>
