---
title: 协议版本
sidebar_position: 3
---

# 协议版本

Minecraft Wiki 中有对该概念的 [详细介绍](https://zh.minecraft.wiki/w/%E5%8D%8F%E8%AE%AE%E7%89%88%E6%9C%AC) 。本文将讲解开服过程中需要注意的重点内容。

简单来说，协议版本就像客户端与服务器之间的语言版本。把旧版协议比作老奶奶，比如“古代人”1.20.50 讲的是“古汉语”协议 630，而你 1.20.60 讲的是“现代汉语”协议 649。
古代人 (1.20.50) 和你 (1.20.60) 讲 (连接) 古汉语 (使用协议 630) 时，你听不懂。同理，你 (1.20.60) 和古代人 (1.20.50) 讲 (连接) 现代汉语 (使用协议 649) 时，古代人听不懂。
为了能互相理解，你只能和同为现代人 (同为 649 协议) 交谈，古代人也只能和古代人 (同为 630 协议) 交谈。

## 更新规律

通常来说，MOJANG 会在每个修订版本号十位发生改变时改变协议版本。例如，1.20.10 - 1.20.15 同为协议版本 594，而 1.20.30 开始协议版本变更为 618。
之后的 1.20.31、1.20.32 都是 618 协议版本，到 1.20.40 时再次更新为 622。

有时 MOJANG 会为了一些原因和目的在十位之内更新协议版本，例如 1.19.60 - 62 和 1.19.63 协议版本不同，1.21.0 - 1.21.1 和 1.21.2 - 1.21.3 协议版本不同。

通常 MOJANG 会每五周左右发布一次这种更新协议的版本。
