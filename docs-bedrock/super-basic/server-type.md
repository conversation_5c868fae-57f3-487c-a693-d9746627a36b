---
title: 服务器类型
sidebar_position: 2
---

<!-- markdownlint-disable no-duplicate-heading -->

# 服务器类型

> 目前有五类主流的选择：纯净服 (又名原版服)，Addon 服 (仅 BDS)，插件服，小游戏服 (插件服的一种)，Addon 和 插件混合服 (仅 BDS)。

在 Minecraft 基岩版 的服务器世界中，有多种不同的服务器类型供腐竹选择。每种类型都有其独特的特点和玩法。以下是五种主流的服务器类型，帮助你根据自己服务器的需求做出选择。

## 1. 纯净服 (又名原版服)

### 定义

纯净服是完全遵循 Minecraft 基岩版 官方游戏规则的服务器，不添加任何额外的插件或修改。

### 特点

- **原汁原味**：提供最纯正的游戏体验，玩家可以享受到官方设计的游戏内容和挑战。
- **稳定性**：没有插件干扰，服务器运行更加稳定。
- **低维护**：无需担心插件兼容性和更新问题，维护相对简单。

### 适用对象

- 喜欢传统 Minecraft 游戏体验的玩家。
- 腐竹希望专注于提供稳定和公平的游戏环境。

## 2. Addon 服 (仅 BDS)z

### 定义

Addon 服 是基于官方 BDS 的服务器，通过添加官方支持的 **Addon** 来 **扩展游戏功能**。

### 特点

- **官方支持**：Addon 由 Mojang 官方 提供，保证了与游戏版本的兼容性。
- **有限定制**：允许一定程度的游戏内容扩展，如新的方块、物品和生物。
- **稳定性**：Addon 经过官方测试，对服务器稳定性影响较小。

### 适用对象

- 希望在官方服务器基础上增加一些自定义内容的玩家。
- 腐竹希望保持服务器官方特性的同时，提供更多玩法。

## 3. 插件服

### 定义

插件服是指使用 第三方服务器端软件 (如 LiteLoader、Levilamina、PocketMine-MP、PowerNukkitX 等)，通过安装插件来大幅度扩展游戏功能的服务器。

### 特点

- **高度自定义**：插件种类繁多，可以实现丰富的游戏模式和功能。
- **社区支持**：有大量开发者提供的插件和教程，方便腐竹和玩家。
- **兼容性问题**：可能存在插件间兼容性和服务器稳定性的问题。

### 适用对象

- 喜欢多样化游戏体验和特殊玩法的玩家。
- 愿意投入时间学习和配置插件的腐竹。

## 4. 小游戏服 (插件服的一种)

### 定义

小游戏服 是插件服的一种特殊形式，专注于提供各种小型游戏和挑战，如战墙、空岛、迷宫等。

### 特点

- **娱乐性强**：提供快速、有趣的游戏体验，适合短时间游玩。
- **社交互动**：玩家可以在游戏中互动，增加社区凝聚力。
- **定期更新**：小游戏内容经常更新，保持新鲜感。

### 适用对象

- 喜欢快节奏和竞技性游戏的玩家。
- 希望通过小游戏吸引和保持玩家活跃的腐竹。

## 5. Addon 和 插件混合服 (仅 BDS)

### 定义

这种服务器结合了 Addon 服 和 插件服 的特点，既使用 官方 Addon，也通过第三方插件来扩展功能。

### 特点

- **综合优势**：结合了 Addon 的官方支持和插件的高度自定义。
- **复杂配置**：需要同时管理 Addon 和 插件，配置较为复杂。
- **性能要求**：可能对服务器性能有更高的要求。

### 适用对象

- 希望在保持官方特性的同时，拥有更多自定义玩法的玩家。
- 有经验的腐竹，能够处理 Addon 和 插件 的兼容性问题。
选择合适的服务器类型是打造成功 Minecraft 服务器的重要步骤。根据你的目标、玩家群体和技术能力，合理选择服务器类型，为玩家创造一个有趣、稳定且充满活力的游戏环境。
