---
title: 服务器版本选择
sidebar_position: 4
---

# 选择合适的版本

先说结论：如果没有明确需求，**选择对应服务端的最新版即可。** 最新版的服务端往往支持最新版的客户端进入。

## 版本差异

各个版本更新的内容可以在 [Minecraft Wiki](https://zh.minecraft.wiki) 找到，此处不对游戏内容做过多叙述。除了关注游戏内容上的差异外，你还需要注意以下几个被官方轻描淡写，但对玩家有重大影响的客户端方面的更新：

- 1.20.50：由于该版本中有许多方块进行了“扁平化”，经过了代码重构，导致一些该版本以下的跨版本插件无法很好地兼容该版本及以上版本。
- 1.20.40：该版本中新的触控布局可以自定义位置
- 1.20.x(没有确切版本，望大佬补充) ：大约从此版本开始，安卓版本出现了严重的卡顿问题，该问题截至撰写本文时仍未修复，社区也没有任何解决方案。
- 1.19.50：该版本加入了新的触控布局
- 1.18.30：从该版本开始，由于客户端实装了渲染龙，客户端无法使用传统的光影，只能使用硬件光追或延迟渲染改善游戏画面。

## 喜欢“自动更新”的玩家们

本意上为了确保用户第一时间接收到最新功能和错误修复，包括 App Store、Microsoft Store、Google Play 在内的主流应用商店都具备了自动更新 app 的功能，并且推荐用户开启。

然而这个功能在基岩版上却造成了这样的问题：由于基岩版每隔若干版本都要更换协议，这就导致一些玩家的客户端在其不知情的情况下自动更新，并导致他们无法进入服务器，遇到类似“所有的玩家都应该更新到最新版本然后再试一次”的提示。在 Java 版上这个问题很少见到，因为只有官启中有类似自动更新的机制，而玩家们并不都习惯于用官启。

为了让这些玩家们能够进入服务器，你需要持续不断地把你的服务器更新最新版，或者要求玩家们必须关闭自动更新或使用指定游戏安装包。然而后者会带来这个问题：
由于 iOS 系统的机制原因，iOS 玩家无法使用特定的安装包安装游戏，他们只能下载安装最新版本的游戏。iOS 玩家的这个问题是社区一直无法完美解决的相当棘手的问题，在后面的章节中，我们会陆续介绍一些折中的方案来让你应对这些 iOS 玩家。

除了自动更新机制外，玩家们仍然倾向于使用最新版，主要因为以下原因：

- Microsoft Store、App Store、Google play、第三方应用商店提供的破解版安卓版安装包会优先下载安装最新版
- 社区资讯媒体都倾向于发表最新版游戏相关内容，玩家们会在这些信息源的引导下优先选择最新版
- 基岩版游戏在任何一个平台切换版本都不容易，因此社区形成了全员跟进最新版这一不成文的规定。如果玩某旧版本的服务器，玩家就只能玩所有该指定版本的服务器，要玩其他服务器时只能进行繁琐的操作切换版本，自由度大打折扣。

这些因素均导致了玩家们始终倾向于玩新版服务器。

## 权衡版本间的利弊

:::tip

除非你有明确需求，任何新手服主都应选择支持最新版客户端加入的服务器

:::

在选择版本时，你**首先需要考虑是否需要强制玩家固定版本。**

上文已经介绍了玩家倾向于自动更新的行为。如果你强制玩家固定版本，将产生以下后果：

1. 你将失去几乎所有 iOS 玩家。
2. 你将失去所有只玩最新版的玩家。**由于跟进最新版的服务器数量庞大，对应的玩家群体也非常庞大，失去他们将是重大损失** 。
3. 你的客服团队需要人工指导一些完全不会安装 appx 或 apk 格式安装包的小白。

如果你确定这些后果不会严重影响服务器的受欢迎度，你就可以选择特定版本。在选择版本时，除了根据游戏内容进行选择外，你还需要考虑玩家客户端所具备的功能，尤其是渲染龙、安卓版本的卡顿问题等饱受社区诟病的问题。选择让服务器匹配一个游戏体验较好的客户端版本，可以在一定程度上提高玩家在你服务器中的游戏体验。
