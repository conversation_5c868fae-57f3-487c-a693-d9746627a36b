---
sidebar_position: 5
title: 生物实体
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

# 生物实体

<Tabs queryString="meg">
<TabItem value="meg" label="ModelEngine">

此插件可以帮助你打包资源包生物模型

闭源付费插件，并且 v3 和 v4 分开付费，v3 用户想用 v4 得再买一次

- v3 支持 1.16 - 1.20
- v4 支持 1.19 - 1.21

:::info

`插件百科` https://mineplugin.org/ModelEngine

:::

## v3

:::info

`SpigotMC` https://www.spigotmc.org/resources/.79477/

`文档 (英文)` https://github.com/Ticxo/Model-Engine-Wiki/wiki

`文档 (中文)` https://inrhor.gitbook.io/modelengine-wen-d

:::

它有一个 [demo](https://www.spigotmc.org/resources/.106521/) 版，免费但只能注册 6 个模型

## v4

:::info

`MythicCraft` https://mythiccraft.io/index.php?resources/.1213/

`文档 (英文)` https://git.lumine.io/mythiccraft/model-engine-4/-/wikis/home

`文档 (中文)` https://gitlab.com/TranslatedByShark/ModelEngine-Manual-CN/-/wikis/home

:::

[ModelEngineDecoration](https://www.spigotmc.org/resources/106916/) 是 ModelEngine V4 的一个扩展，可以把生物实体变成装饰

</TabItem>
<TabItem value="bm" label="BetterModel">

:::info

`SpigotMC` https://www.spigotmc.org/resources/121561

`Hangar` https://hangar.papermc.io/toxicity188/BetterModel

`Modrinth` https://modrinth.com/plugin/bettermodel

`MineBBS` https://www.minebbs.com/resources/.10158

`GitHub` https://github.com/toxicity188/BetterModel

:::

免费的生物实体插件，相对于 ModelEngine 在网络上优化较好，支持的版本为 1.20.6 - 1.21

</TabItem>

<TabItem value="fmm" label="Free Minecraft Models">

:::info

`SpigotMC` https://www.spigotmc.org/resources/.111660

`GitHub` https://github.com/MagmaGuy/FreeMinecraftModels

`文档 (英文)` https://github.com/MagmaGuy/FreeMinecraftModels

:::

</TabItem>
</Tabs>
