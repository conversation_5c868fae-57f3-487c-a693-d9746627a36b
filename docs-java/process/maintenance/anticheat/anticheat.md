---
sidebar_position: 2
title: 反作弊
slug: /anticheat
---

# 概览

在各种各样的 Minecraft 服务器中都存在各种各样的 **Hacker Client User** (作弊端用户)

作弊者通过作弊软件获取其他合法玩家无法取得的优势，从而影响游戏平衡性。

各服主和合法玩家都会为解决这些作弊端用户头痛不已。所以这章来了解一些反作弊插件

## 作弊类型

一般认为作弊是以任意形式取得合法玩家无法取得的优势的行为。

一般认为的作弊包括但不限于：

- 种子推演
- RNG(随机数) 预测
- 自动前往、药品、举盾、挖矿等
- 刀刀暴击、杀戮光环等战斗作弊
- 穿墙、高跳、飞行等移动作弊
- ......

## 注意

反作弊在尽量不影响合法玩家游玩的前提下，**惩戒**和**阻拦**作弊玩家。

但由于 Minecraft 协议的特殊性，很多数据是服务端难以判断是否合法的。

作弊和反作弊永远是一个你追我赶的永无休止的竞赛，你无法抓到每一个作弊者。

**_最好的反作弊是社区，一个良好的社区会自发的举报作弊。_**
