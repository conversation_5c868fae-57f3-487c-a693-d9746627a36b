---
title: 在 Android 上安装 PojavLauncher
sidebar_position: 1
---

# 在 Android 上安装 PojavLauncher

:::warning

 PojavLauncher已于2025年5月17日宣布停更

 建议使用Amethyst或者FCL替代PojavLauncher
 
:::

GitHub Action 下载

**需要 GitHub 账户才能下载。**

如果你想在正式发布前测试最新、最好的版本，可以从 GitHub Actions 获取副本。

<!--markdownlint-disable ol-prefix-->

1. 首先进入 [PojavLauncher](https://github.com/PojavLauncherTeam/PojavLauncher) 软件仓库，然后切换到 `Actions` 选项卡。

![](https://pojavlauncherteam.github.io/assets/img/Android-Actions-1.96a0b3c7.png)

2. 接下来，在工作流程选择菜单中选择 `Android CI`。

![](https://pojavlauncherteam.github.io/assets/img/Android-Actions-2.99495cb5.png)

3. 选择要下载的分支旁边带有绿色或蓝色复选标记的构建。
    - 大多数人都会选择 `v3_openjdk`。

![](https://pojavlauncherteam.github.io/assets/img/Android-Actions-3.d484abce.png)

4. 在 "Artifacts"下，选择要下载的构建类型。
    - 大多数人都会选择 `app-debug`。

![](https://pojavlauncherteam.github.io/assets/img/Android-Actions-4.07b1b65e.png)

<!--markdownlint-enable ol-prefix-->

下载完 Actions 版本后，就可以解压缩并像安装其他 .apk 文件一样进行安装了。
