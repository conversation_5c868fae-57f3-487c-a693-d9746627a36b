---
title: 核心选择
sidebar_position: 1
---

## 核心选择

:::info

你可以点击名称一栏中的字体跳转并找到下载

下载镜像只是备选选项

:::

### 关于 waterfall

[关于 Waterfall 停止维护的公告](https://forums.papermc.io/threads/announcing-the-end-of-life-of-waterfall.1088/)。

:::info

停止维护是指不再为其添加新功能或错误修补，

Waterfall 仍然会合并来自 BungeeCord 的更改。

对于仍然想使用 Waterfall 的人来说这可能不是一个很大的影响。

:::

目前为止，你可以选择以下核心进行转发：

<!--markdownlint-disable line-length-->

| 名称                                                        | 介绍                                      | 推荐与否 | 下载镜像                                                                                                                                                                                                                         |
|-----------------------------------------------------------|-----------------------------------------|------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| [Waterfall](https://papermc.io/software/waterfall)        | Papermc 对 BungeeCord 的分支，但已停止向其添加新功能    | 不推荐  | [McRes](https://mcres.cn/downloads/waterfall.html) [FastMirror](https://www.fastmirror.net/#/download/Waterfall) [原子云](https://res.nullatom.com/Minecraft/Server/Waterfall/) [MCSL](https://sync.mcsl.com.cn/core/Waterfall) |
| [Lightfall](https://github.com/ArclightPowered/lightfall) | Arclight waterfall 的分支，支持 Forge 玩家进入服务器    | -    | [McRes](https://dev.mcres.cn/job/Lightfall/lastBuild/) [FastMirror](https://www.fastmirror.net/#/download/lightfall) [MCSL](https://sync.mcsl.com.cn/core/Lightfall)                                                         |
| [Velocity](https://papermc.io/software/velocity)          | 新生代代理端核心，拥有高安全性和高性能，但是插件不如 BungeeCord 那边多 | 非常推荐 | [McRes(自己点进去找)](https://mcres.cn/) [FastMirror](https://www.fastmirror.net/#/download/Velocity) [原子云](https://res.nullatom.com/Minecraft/Server/Velocity/) [MCSL](https://sync.mcsl.com.cn/core/Velocity)                    |
| [Velocity-CTD](https://github.com/GemstoneGG/Velocity-CTD) | Velocity 的分支，包含各种优化、命令和更多功能，如 Redis 支持、队列系统、管理命令等 | 推荐 | [GitHub Releases](https://github.com/GemstoneGG/Velocity-CTD/releases) |
| [BungeeCord](https://github.com/SpigotMC/BungeeCord)      | 最古老的代理端核心，甚至有网页版 mc 的核心                   | -    | [McRes](https://repo.wdsj.io/repository/Bungeecord/BungeeCord.jar) [FastMirror](https://www.fastmirror.net/#/download/BungeeCord) [MCSL](https://sync.mcsl.com.cn/core/BungeeCord)                                           |
| [Gate](https://gate.minekube.com/)                        | 基于 Go 开发的核心，拥有超高的性能和极低内存占用。               | -    | -                                                                                                                                                                                                                            |
| [Travertine](https://github.com/PaperMC/Travertine)       | Waterfall 1.7.10 的分支                    | -    | -                                                                                                                                                                                                                            |

富哥的选择

| 名称                                                                               | 介绍                                                  | 价格          |
|----------------------------------------------------------------------------------|-----------------------------------------------------|-------------|
| [NullCordX](https://polymart.org/resource/nullcordx.1476/updates)                | 基于反机器人原生构建的复杂瀑布叉，提高了性能                              | 10 美元        |
| [XCord](https://builtbybit.com/resources/xcord-high-performance-anti-bot.16843/) | BC 叉：高级反机器人  性能 反漏洞利用 Anti-SpigotExploit(1.7-1.20.4) | 10 美元        |
| [FlameCord](https://www.flamecord.com/)                                          | 终极反机器人解决方案，Anti-VPN 和高性能 BungeeCord 分叉，适用于 Minecraft 服务器 | 6 美元 (永久 15 美元) |

<!--markdownlint-enable line-length-->

