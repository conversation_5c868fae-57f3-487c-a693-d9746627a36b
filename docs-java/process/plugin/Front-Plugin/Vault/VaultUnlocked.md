---
title: VaultUnlocked
sidebar_position: 1
---

# VaultUnlocked

Vault 的分支版本，提供更多功能

## 安装

将 `VaultUnlocked.jar` 放入 `plugins` 文件夹中，其余将自动处理。如果你想调整配置，可修改配置文件，但通常情况下并不需要。

详情参阅 AdvancedConfiguration 部分。

## 功能

- 无需源代码集成：作为独立插件运作，只需引入 jar 文件即可。避免多个插件冲突，便捷集成。
- 广泛插件兼容：支持经济和权限类型的插件抽象层，提升兼容性。

## 特性

- 多货币支持
- 更友好的 PR
- Folia 支持

## 权限

- vault.admin：拥有权限的玩家将收到更新提醒

## 链接

:::info

`Bukkit` https://dev.bukkit.org/projects/vault

`SpigotMC` https://www.spigotmc.org/resources/.117277

`Modrinth` https://modrinth.com/plugin/vaultunlocked

`MineBBS` https://www.minebbs.com/resources/.10014

`GitHub` https://github.com/TheNewEconomy/VaultUnlocked

:::

笨蛋镜像下载 [点击这里](https://dl.8aka.org/plugins/VaultUnlocked-2.3.0.jar)

## 联系

[![](https://img.shields.io/badge/Discord-creatorfromhell-blue?logo=Discord)](https://discord.gg/WNdwzpy) \<- 点击加入 作者的 Discord

## Bstats

[![](https://bstats.org/signatures/bukkit/VaultUnlocked.svg)](https://bstats.org/plugin/bukkit/VaultUnlocked/22252)
