---
title: Terra
sidebar_position: 1
---

# Terra

与其他生成器（如 RTG 依赖代码层修改）不同，Terra 不预设任何地形或生物群系规则，所有生成逻辑（地形噪声、结构分布、植被生成等）均通过配置文件动态定义

## 官方预设

![](_images/terra-1.png)

![](_images/terra-2.png)

![](_images/terra-3.png)

![](_images/terra-4.png)

![](_images/terra-5.png)

## 社区扩展

![](_images/terra-community-1.png)

![](_images/terra-community-2.png)

![](_images/terra-community-3.png)

![](_images/terra-community-4.png)

![](_images/terra-community-5.png)

![](_images/terra-community-6.png)

![](_images/terra-community-7.png)

## 链接

:::info

`SpigotMC` https://www.spigotmc.org/resources/.85151

`Modrinth` https://modrinth.com/plugin/terra

`MineBBS` https://www.minebbs.com/resources/.8278

`GitHub` https://github.com/PolyhedralDev/Terra

`文档(英文)` https://terra.polydev.org/install/index.html

`插件百科` https://mineplugin.org/Terra

:::

## Bstats

[![](https://bstats.org/signatures/bukkit/Terra.svg)](https://bstats.org/plugin/bukkit/Terra/9017)
