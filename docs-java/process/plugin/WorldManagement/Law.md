---
sidebar_position: 2
sidebar_label: 保护 - Law
---

# Law

<a href="https://github.com/mouse0w0/law/releases">
  <img src="https://img.shields.io/github/v/release/mouse0w0/law" class="stylish-image" alt="Release" />
</a>
<a href="https://github.com/mouse0w0/law">
  <img src="https://img.shields.io/github/languages/code-size/mouse0w0/law" class="stylish-image" alt="Code Size" />
</a>
<a href="https://github.com/mouse0w0/law/blob/main/LICENSE">
  <img src="https://img.shields.io/github/license/mouse0w0/law" class="stylish-image" alt="License" />
</a>

本插件基于 Bukkit API 开发，用于在服务器中控制实体和方块行为，并为不同世界设置不同规则。

基于 1.16.5 开发，但已完成全版本兼容，除部分功能无法在较低版本运行外，其余功能均可运行。插件功能如下所示：

- 阻止实体生成、通过传送门、爆炸、破坏方块、乘坐载具、捡起物品。
- 阻止玩家受到伤害、放置方块、破坏方块、点击方块和实体。
- 阻止物品受到爆炸、火焰和岩浆伤害。
- 阻止生物转变（村民、女巫、僵尸村民、溺尸、苦力怕、僵尸猪人、蘑菇牛）。
- 阻止火焰蔓延、烧毁方块、熄灭。
- 阻止雪形成、融化。
- 阻止雪傀儡产雪。
- 阻止冰形成、融化。
- 阻止珊瑚失水。
- 阻止踩碎海龟蛋。
- 阻止耕地退化。
- 阻止树叶腐烂。
- 阻止龙蛋传送。
- 阻止床和重生锚爆炸。
- 阻止水和岩浆流动。
- 死亡时保留经验和物品。
- 控制天气。
- 阻止方块被点燃。

## 指令

- `/law help` - 查看插件指令帮助。
- `/law reload` - 重载插件的配置文件。
- `/law world` - 查看当前所在世界名。
- `/law query` - 切换查询模式（点击方块或实体查看其名）。

## 权限

- `law.admin.help` - 查看插件指令帮助。
- `law.admin.reload` - 重载插件的配置文件。
- `law.admin.world` - 查看当前所在世界名。
- `law.admin.query` - 切换查询模式（点击方块或实体查看其名）。
- `law.bypass.place-block` - 绕过放置方块检查。
- `law.bypass.break-block` - 绕过破坏方块检查。
- `law.bypass.left-click-block` - 绕过左键点击方块检查。
- `law.bypass.right-click-block` - 绕过右键点击方块检查。
- `law.bypass.use-item` - 绕过使用物品检查。
- `law.bypass.left-click-entity` - 绕过左键点击实体检查。
- `law.bypass.right-click-entity` - 绕过右键点击实体检查。

## 链接

:::info

`MineBBS` https://www.minebbs.com/resources/.9067

`GitHub` https://github.com/mouse0w0/law

:::

# Bstats

[![](https://bstats.org/signatures/bukkit/Law.svg)](https://bstats.org/plugin/bukkit/Law/16878)
