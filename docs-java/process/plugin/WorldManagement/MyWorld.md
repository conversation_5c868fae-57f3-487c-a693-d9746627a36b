---
sidebar_position: 5
sidebar_label: 多世界 - MyWorld
---

# MyWorld

> 另一个多世界插件。

该插件包含所有关于多世界的一切功能：

- 加载、卸载、复制、删除、保存和创建世界（[点击查看命令列表](https://wiki.traincarts.net/p/MyWorlds/Commands)）
- 显示世界的信息并列出所有的世界和可用的区块等信息
- 设置世界游戏模式，难度，时间，天气，自动保存，是否开启PvP等等
- 设置世界生物生成限制
- 清空世界时清除玩家
- 在一定程度上修复受损的世界
- 创建跨世界、单向、简单的传送门，无需命令
- 使用权限配置每个世界特定的聊天
- 服务器启动时自动加载世界
- 权限细化，您可以设置某个玩家可以进入某个世界
- 可以将玩家从一个世界传送到另一个世界
- 可以设置特定世界某玩家拥有 OP 权限，便于管理（可以用与创造）
- 易于记忆的命令：可以配置自定义命令和帮助
- 背包隔离、合并和禁用（清空）
- 可配置玩家出生的主世界
- 可以设置为重生世界的所有玩家
- 记住玩家在某个世界中的最后一个已知位置，并将他传送到该位置（[点击查看功能详情](https://wiki.traincarts.net/p/MyWorlds/Rlpp)）

## 链接

:::info

`SpigotMC` https://www.spigotmc.org/resources/.39594

`MineBBS` https://www.minebbs.com/resources/myworlds.10288

`GitHub` https://github.com/bergerhealer/MyWorlds

`文档` https://wiki.traincarts.net/p/MyWorlds

:::
