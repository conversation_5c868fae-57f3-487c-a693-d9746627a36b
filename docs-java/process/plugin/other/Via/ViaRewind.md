---
sidebar_position: 4
---

# ViaRewind

:::info

`SpigotMC` https://www.spigotmc.org/resources/.52109

`Hangar` https://hangar.papermc.io/ViaVersion/ViaRewind

`Modrinth` https://modrinth.com/plugin/viarewind

`GitHub` https://github.com/ViaVersion/ViaRewind

:::

ViaRewind 可以让超低版本 (1.7-1.8) 客户端进入高版本服务器

需要安装 [ViaRewind](https://ci.viaversion.com/view/ViaRewind/job/ViaRewind) 和
[ViaRewind Legacy Support](https://ci.viaversion.com/view/ViaRewind/job/ViaRewind%20Legacy%20Support) 才能发挥兼容性

## FAQ

:::warning

ViaRewind 的兼容性非常差，毕竟跨版本这么大必然有问题

所以解决问题的最好方法就是删除

:::

## 配置文件

* 1.9 版本的粒子是否会被 1.8 及更低版本的类似粒子替换

打开 `replace-adventure`
