---
title: Authme
sidebar_position: 2
---

# Authme

:::info

`Bukkit` https://dev.bukkit.org/projects/authme-reloaded

`SpigotMC` https://www.spigotmc.org/resources/.6269/

`Hangar` https://hangar.papermc.io/0D00_0721/AuthMeReReloaded

`Modrinth` https://modrinth.com/plugin/authmereloaded

`GitHub(原版)` https://github.com/AuthMe/AuthMeReloaded

`GitHub(电棍的fork)` https://github.com/HaHaWTH/AuthMeReReloaded

`文档(英文)` https://github.com/AuthMe/AuthMeReloaded/wiki

`文档(中文)` https://pluginscdtribe.github.io/wiki/authme

`插件百科` https://mineplugin.org/Authme

:::

老牌登录插件 Authme

建议使用[电棍的 fork](https://github.com/HaHaWTH/AuthMeReReloaded)，这是它的[中文介绍](https://github.com/HaHaWTH/AuthMeReReloaded/blob/master/README-zh.md)

如果你也使用了跨服端，请在跨服端安装对应的 AuthMe

[AuthMeBungee](https://www.spigotmc.org/resources/.50219/)

[AuthMeVelocity](../../../../process/cross-server/plugin/cross-only.md#authmevelocity)

## 扩展

### 登录时显示 title

https://www.spigotmc.org/resources/.111370
