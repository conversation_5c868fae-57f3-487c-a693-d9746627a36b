---
title: SkinsRestorer
sidebar_position: 8
sidebar_label: 皮肤 - SkinsRestorer
---

# SkinsRestorer

:::info

`官网` https://skinsrestorer.net

`SpigotMC` https://www.spigotmc.org/resources/.2124

`Hangar` https://hangar.papermc.io/SRTeam/SkinsRestorer

`GitHub` https://github.com/SkinsRestorer/SkinsRestorer

`文档` https://skinsrestorer.net/docs

:::

这个插件可以让盗版 (离线) 服务器使用皮肤。

SkinsRestorer 是一体化设计，没有“单端版” “bc 版”之说，同一个 jar 文件可以直接扔 单端 / BungeeCord / Velocity 的 `plugins` 文件夹里加载

:::tip

**TrMenu**

如果同时在跨服端和子服安装 SkinsRestorer 可能会导致无法获取玩家头颅材质

删除子服的 SkinsRestorer 插件即可

:::
