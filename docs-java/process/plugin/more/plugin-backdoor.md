---
title: 插件排毒
sidebar_position: 5
---

# 插件排毒

:::danger

此工具不可能让你避开 100% 的风险

你应该从正规渠道获取插件，而不是寄希望于此软件

:::

有些时候，当你从一些不可信的来源获得插件（比如闲鱼）的时候，这个时候就需要对插件进行扫描排除潜在威胁，或者你发现你的服务器莫名其妙的多出了几个 OP，你也需要对现有插件进行扫描

McGuard 可以在不启动服务器的情况下对服务端中的全部插件进行扫描，并找出潜在的恶意插件。

## 下载

下载 MCG 的 [jar](https://cd.starkettle.com/MCG.jar) 存放到服务端根目录即可。

## 控制台模式

### 使用

打开 cmd，在服务端根目录运行 `java -Xmx1G -jar MCG.jar`

当 MCG 控制台模式完成启动后，你将见到以下字样：

```text
[McGuard] 感谢您使用 McGuard！
MCGuard v-b0/r0 Author: huzpsb
请选择模式 (0-标准模式 1-专家模式 2-开发者模式)：
```

目前，你只需要选择 **0** 即可，专家模式会在后面提到

### 结果

```text
[提示] 正在扫描 MCG.jar ................
[提示] 正在扫描 Vault.jar ................
[提示] 正在扫描 [C]安全测试_CoRE.jar ................

---------------------------------------------------------
[结果展示][C]安全测试_CoRE.jar
[严重] a/x.class 很有可能存在获取 OP 类后门 (r:set-only)。
[严重] a/x.class 很有可能存在远程命令类后门 (r:processbuilder)。
扫描完成！请按任意键退出
```

对于未检出异常的插件，MCG 不会输出“结果展示”块。例如，上文中的 MCG.jar 和 Vault.jar。

对于存在异常的插件，MCG 会输出“结果展示”块。并给出异常的具体描述。

:::warning

*注意，MCG 不会对插件进行修改，也不会对插件进行删除。你需要手动删除异常插件

:::

### 专家模式

会使用内置的已知恶意代码对插件进行匹配并使用某些规则分析代码的意图，能够检出绝大多数常见的与没有故意规避扫描的恶意插件。存在一定的误报。

## 插件模式

MCG 的插件模式是一个 Bukkit/Spigot 服务端插件。它可以在服务器运行过程中对服务器进行持续的保护，减少漏网之鱼。

**请注意，插件模式的安全是相对的。可能存在极少数恶意插件能够突破 MCG 的行为管控。不要运行存在已知恶意插件的服务端，哪怕你已经安装了 MCG！**

**在运行来源未知服务端前，我们强烈推荐先使用 MCG 的控制台模式进行扫描，并提供一个虚拟化（Sandboxie 等）的环境！**

更详细的教程和 Pro 版本的购买请 [点击](https://starkettle.com/mcg/)
