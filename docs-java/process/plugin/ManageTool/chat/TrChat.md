---
title: TrChat
sidebar_position: 1
---

# TrChat

![](./_images/TrChat/1.png)

## 功能

- 版本兼容，支持 1.8-最新
- 多频道聊天，默认提供多个常用聊天频道（常规/全局/私聊/管理频道）
- 敏感词过滤，十分强大的敏感词过滤系统，支持自定义忽略标点符号判断
- 过滤检测，过滤器不仅仅检测聊天，更会过滤木牌、铁砧等等
- 云端词库，你无需自行添加过多的敏感词，插件将自动从云端更新敏感词库（当前 0.7k+）
- 物品展示，所有频道均支持展示玩家容器内物品，支持指定槽位、同时展示多个物品，支持设置冷却
- 聊天冷却，自定义玩家聊天间隔，防止刷屏
- At@玩家，支持高亮@玩家，并通过音效+TITLE 等方式提醒，支持设置冷却
- 监听私聊，管理员支持切换监听模式，开始时将被推送玩家的私聊内容
- 颜色代码，通过独立的权限控制玩家是否能够使用某颜色代码，支持聊天、书本、铁砧等
- 多聊天格式，单个聊天频道支持多个聊天格式，通过权限优先级筛选
- 跨服支持，不仅仅是跨服私聊、全局喊话，跨服也支持展示物品！
- 自定义正则匹配替换，替换自定义匹配到的内容为自定义 JSON 模块，非常强大
- 支持 PlaceholderAPI 变量
- 支持 Tab 补全 Bungee 全服玩家 ID
- 期维护更新功能
- 免费下载，代码开源，提供 API, 自动检测更新

## 说明

<!--markdownlint-disable line-length-->

<a href="https://github.com/FlickerProjects/TrChat/releases">
  <img src="https://img.shields.io/github/v/release/TrPlugins/TrChat?logo=VirusTotal&style=for-the-badge" class="stylish-image" alt="Version" />
</a>
<a href="https://github.com/FlickerProjects/TrChat/issues">
  <img src="https://img.shields.io/github/issues/TrPlugins/TrChat?logo=StackOverflow&style=for-the-badge" class="stylish-image" alt="Issues" />
</a>
<a href="https://github.com/FlickerProjects/TrChat/commits/v2">
  <img src="https://img.shields.io/github/last-commit/TrPlugins/TrChat?logo=ApacheRocketMQ&style=for-the-badge&color=1e90ff" class="stylish-image" alt="Last Commit" />
</a>
<a href="https://github.com/NEZNAMY/TAB/blob/main/LICENSE">
  <img src="https://img.shields.io/github/downloads/TrPlugins/TrChat/total?style=for-the-badge&logo=docusign" class="stylish-image" alt="Downloads" />
</a>

[TrChat](https://github.com/TrPlugins/TrChat) 是基于 [TabooLib](https://github.com/TabooLib/taboolib) 开发，发布于 2019-8-16 的高级聊天系统插件

1.0~1.7 版本：由 [Arasple](https://github.com/Arasple) 使用 TabooLib 5 开发，最高支持 1.16.5 版本

1.8~1.9 版本：由 [ItsFlicker](https://github.com/ItsFlicker) 在 1.7 版本基础上使用 TabooLib 6 开发，已停止维护

2.0 版本：由 [ItsFlicker](https://github.com/ItsFlicker) 使用 TabooLib 6 重新编写并持续维护

## 链接

:::info

`GitHub` https://github.com/TrPlugins/TrChat

`MineBBS` https://www.minebbs.com/resources/.7245

`文档` https://trchat.trixey.cc

:::

国人插件，一个功能强大的聊天插件

## Bstats

### Bukkit / Spigot

[![](https://bstats.org/signatures/bukkit/TrChat.svg)](https://bstats.org/plugin/bukkit/TrChat/5802)

### BungeeCord

[![](https://bstats.org/signatures/bungeecord/TrChat.svg)](https://bstats.org/plugin/bungeecord/TrChat/5803)

### Velocity

[![](https://bstats.org/signatures/velocity/TrChat.svg)](https://bstats.org/plugin/velocity/TrChat/12541)
