---
title: 权限插件
slug: /permission
sidebar_position: 1
---

# LuckPerms

![](_images/1.png)

## ![介绍](_images/2.png)

当前最先进，现代，支持广泛，用户数量最多的权限插件。

- **响应迅速** - 在开发之初便已经将性能和可扩展性放在首位。
- **深受信赖** - 被数以千计的服务器管理者使用，再大型的服务器也有我们的身影。
- **简易上手** - 通过命令，配置文件，网页版编辑器等方式操作你的权限。
- **高效灵活** - 支持多种不同的存储方式与服务器平台。
- **可客制化** - 为您的服务器提供大量的自定义选项和设置，以便您最大程度客制化使用体验。
- **永远免费** - 此插件基于 MIT 协议开源。

:::tip[注意]

我们只推荐 Luckperms 作为服务器的权限插件。本文档不会涉及其他任何权限插件。

:::

**不建议使用** Luckperms 以外的权限插件如：

- GroupManager
- PermissionsEx
- zPermissions
- bPermissions
- PermissionsBukkit
- PowerRanks
- UltraPermissions
- BungeePerms
- PowerfulPerms

其他权限组插件导入数据到 Luckperms？[点击这里](https://continue-project.netlify.app/LuckPerms/how-to.migrate-from-other-plugins.html)

## ![链接](_images/3.png)

:::info

`官网` https://luckperms.net

`SpigotMC` https://www.spigotmc.org/resources/luckperms.28140

`GitHub` https://github.com/LuckPerms/LuckPerms

`文档(英文)` https://luckperms.net/wiki/Home

`文档(中文)` https://continue-project.netlify.app/LuckPerms

`插件百科` https://mineplugin.org/LuckPerms

:::

## 安装

下载 jar 文件放入 plugins 文件夹即可。

[点击此处查看教程](https://continue-project.netlify.app/LuckPerms/#/install-on-a-single-server)

:::tip[注意]

1.7.10 要用 Bukkit Legacy 版本。

:::

## 存储方式

LuckPerms 可以使用 yml 文件存储数据，易于编辑，但我推荐使用 h2（默认）或者连接数据库使用。

如果你想更改存储方式，可见：https://continue-project.netlify.app/LuckPerms/storage.html
