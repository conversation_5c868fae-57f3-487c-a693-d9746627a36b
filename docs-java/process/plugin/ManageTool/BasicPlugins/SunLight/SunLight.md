---
title: SunLight
sidebar_position: 3
---

# SunLight

![](_images/1.png)

<a href="https://discord.gg/EwNFGsnGaW">
  <img src="_images/2.png" class="stylish-image" alt="" />
</a>
<a href="https://ko-fi.com/nightexpress">
  <img src="_images/3.png" class="stylish-image" alt="" />
</a>
<a href="https://nightexpress.gitbook.io/sunlight">
  <img src="_images/4.png" class="stylish-image" alt="" />
</a>

## 特点

![](_images/5.png)

- **模块化。** 插件的任何部分都可以轻松完全禁用！包括任何插件命令！
- **轻盈而现代。** 漂亮的消息设计，简单直观的 GUI，没有过载（hello CMI），也不是那么基本（hello Essentials）。
- [**JSON 支持。**](https://github.com/nulli0n/NexEngine-spigot/wiki/Language-Config#json-formatting) 大约 90% 的插件消息（命令用法和描述除外）支持自定义 JSON 元素！
- **GUI 驱动。** 忘记在聊天中浏览套件、经线、房屋、禁令等。所有这些都是通过完全可定制的信息 GUI 完成的！
- **常用命令。** 超过 50+ 基本和实用命令！
- **更多命令。** 总共 100+ 个命令，启用所有模块！
- **命令快捷方式。** 为任何 SunLight 命令创建自定义快捷方式。示例：/gamemode creative -> /gmc！
- **命令冷却时间。** 根据玩家的等级为任何服务器命令创建自定义冷却时间。
- [**命令设置。**](https://github.com/nulli0n/SunLight-spigot/wiki/Commands#-commands-config) 某些命令在 commands.yml 配置文件中具有自己的设置。
- **离线玩家支持。** 几乎每个 SunLight 命令都支持离线玩家！
- **自定义文本文件。** 使用任何文本创建自定义.txt，并使用分配给该文件的唯一命令进行打印！非常适合为命令创建 motd、规则甚至自定义文本 GUI！
- [**占位符 API**](https://github.com/nulli0n/SunLight-spigot/wiki/PlaceholderAPI) 支持。

## 链接

:::info

`SpigotMC` https://www.spigotmc.org/resources/.67733

`MineBBS` https://www.minebbs.com/resources/.7241

`GitHub` https://github.com/nulli0n/SunLight-spigot

`文档(英文)` https://github.com/nulli0n/SunLight-spigot/wiki

`插件百科` https://mineplugin.org/SunLight-Core

:::
