---
title: 基础插件
slug: /Essentials
sidebar_position: 2
---

# 基础插件

基础插件，顾名思义，是大部分服务器 (特别是生存服) 必备的插件。

基础插件一般能提供各种常用功能，比如：tpa、home、管理工具和礼包功能等等...

大部分基础插件还提供了经济功能，如果你没有跨服需求，也可以尝试使用。

<!--markdownlint-disable line-length-->

现有的两个较常用的基础插件，分别是 [EssentialsX](https://essentialsx.net/downloads.html) 和
[CMI](https://www.spigotmc.org/resources/cmi-298-commands-insane-kits-portals-essentials-economy-mysql-sqlite-much-more.3742/) 。

import DocCardList from '@theme/DocCardList';

<DocCardList />
