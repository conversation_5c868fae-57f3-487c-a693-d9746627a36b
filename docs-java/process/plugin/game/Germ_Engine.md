---
title: 萌芽引擎
sidebar_position: 5
---

# 萌芽引擎

![](_images/Germ_Engine/1.png)

![](_images/Germ_Engine/2.jpg)

[![](_images/Germ_Engine/3.png)](http://docs.germmc.com)
[![](_images/Germ_Engine/4.png)](https://jq.qq.com/?_wv=1027&k=qcYW2h20)
[![](_images/Germ_Engine/5.png)](https://space.bilibili.com/321787115)

## ![介绍](_images/Germ_Engine/6.png)

萌芽引擎是基于 Minecraft 1.12.2 版本，以插件 (服务端) + MOD(客户端) 的方式，
站在巨人的肩膀上实现了全面且丰富的在服务端制作各种客户端功能的方法。
使用萌芽引擎可以很大的程度拓展游戏内容、提升玩家游玩体验。
他包含了多种功能其主要的有时装渲染、界面拓展、模型、动作、道具拓展……
萌芽引擎经历了 4 年时间的成长 50 多个版本迭代，目前同时在线客户端超过 5 万，
并且在 300 多台服务器上同时运行。这使得它成为了当今国内 Minecraft 内容制作的潮流。

与 [龙之核心](Dragon_Core.md) 为同类型插件

## 链接

:::info

`MineBBS` https://www.minebbs.com/resources/.7328

`文档` http://docs.germmc.com

:::

萌芽社区：http://forum.germmc.com/home

萌芽控制台：http://admin.germmc.com

萌芽引擎 JavaDoc：http://javadoc.germmc.com    1.12.2-Bukkit：http://bukkitapi.germmc.com

旧版 WIKI①(已过时)：http://wiki.germmc.com       旧版 WIKI②(已过时)：http://engine.germmc.com
