---
title: 中文游戏名
sidebar_position: 7
---

# 使用中文名进入服务器

Minecraft 自 `1.18` 版本后，默认不再允许使用 _除英文字母、数字、下划线以外的_ 字符作为游戏名。
而 [CnUsername](https://github.com/0XPYEX0/CnUsername) 被设计用于解决此痛点。

## 安装途径

| 方式             | 优点                         | 缺点                                      |
| ---              | ---                          | ---                                       |
| `JavaAgent` 加载 | 操作权限高，可以完成所有操作 | 需要自定义命令行，部分面板服可能不支持    |
| 作为`插件`加载   | 灵活，即装即用            | 部分操作无法实现，例如无法修改原版命令选择器 |

:::tip

建议所有有条件的服主都使用 JavaAgent 模式加载

:::

正确安装后，在启动器中填入中文名字正常启动，即可开始享受游戏

CnUsername 与某些昵称插件不同：CnUsername 是真正实现中文名，而非披着中文名的幌子，里子还是英文名的“游戏昵称 (显示名称)”

后续教程及下载请查阅 [GitHub](https://github.com/0XPYEX0/CnUsername)
