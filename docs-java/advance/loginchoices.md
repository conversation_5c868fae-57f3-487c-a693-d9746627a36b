---
title: 登录方式
sidebar_position: 5
---

# 登录方式

在开启服务器之前，选择合适的登录方式至关重要。这不仅关系到玩家们的 **生命财产安全**，还涉及到服务器的管理和稳定性。因此，请在选择时务必 **慎重** 考虑！

:::danger

一旦选定了登录方式，请尽量不要更换它，除非你了解这样做的风险和后果！

:::

## 通用外置登录

通用外置登录是基于分析 `Minecraft 官方 Yggdrasil 认证服务器` 的行为总结出来的一种外置登录方式。

它的特点是玩家只需在 `Minecraft 启动器` 上完成账号登录，在进入服务器时会使用会话登录自动加入游戏。

在这种登录方式下，玩家的游戏档案 (包括皮肤和玩家 UUID) 由 `Yggdrasil` 决定。因此，选择一个 **可信任** 的外置身份验证服务器非常非常非常非常重要。

### 正版登录

正版登录是绝大多数服务端默认设置的登录方式，仅允许 Minecraft Java 正版 用户加入游戏，使用 Mojang 的身份认证服务器来验证玩家身份和分配游戏档案数据。

**优点：**

* 数据包通讯全程加密，确保游戏数据安全。
* 服务端默认配置，无需额外配置。
* 身份验证服务值得信赖。
* 支持自定义角色皮肤以及更改角色名字。

**缺点：**

* 身份验证服务器位于国外，可能因网络问题导致会话验证失败。
* 需要购买正版游戏，限制了一部分玩家。

### Authlib-Injector 实现的外置登录

[Authlib-Injector](https://github.com/yushijinhun/authlib-injector) 是一个允许 Minecraft 使用自定义 Yggdrasil 验证服务器的工具。它可以提供与正版登录几乎相同的游戏体验，但使用的是非官方的身份验证服务器。

:::info

Authlib-Injector 仅作为工具，它本身不提供认证服务。你需要找到一个合适的、完全实现了
[Yggdrasil 服务端技术规范](https://github.com/yushijinhun/authlib-injector/wiki/Yggdrasil-%E6%9C%8D%E5%8A%A1%E7%AB%AF%E6%8A%80%E6%9C%AF%E8%A7%84%E8%8C%83)
的认证服务提供商，比如：

* [LittleSkin](https://littleskin.cn/)
* [红石皮肤站](https://mcskin.com.cn/)

:::warning
**不能确保以上列出的所有服务提供商绝对安全可靠，请注意甄别**
:::

**优点：**

* 继承了正版登录的部分优点。
* 服务商可自行选择。

**缺点：**

* 需要使用受支持的启动器。
* 配置和登录相对复杂，有一定的接受和学习成本。
* 部分玩家可能不接受非正版的外置登录。
* 需要考虑认证服务的可信度。

:::danger

由于 Yggdrasil 拥有绝对的游戏外账号管理权，即使在游戏内实施了多重安全保障措施，也无法完全确保账号安全。因此，建议选择 **值得信赖的服务商** 或自行搭建认证服务器！

在极端情况下，如果你的竞争对手贿赂或收买了你正在使用的 Yggdrasil 服务提供商，你的服务器就完了。最轻的情况是所有用户都获得了管理权限 (全员 OP)，而最严重的情况则可能导致服务器完全崩溃，造成不可估量的损失！！！

:::

## 内置登录

内置登录，也称为离线模式，允许未经过认证的用户以自定义用户名加入游戏，此种模式下，
玩家只需输入任意有效的游戏 ID 即可加入服务器，皮肤是随机分配的，UUID 则由服务器根据用户名生成。

:::danger

内置登录模式安全性较低，通常在玩家登录服务器后才进行验证。
在验证通过之前，玩家在游戏中的行为可能仅依靠第三方登录插件来限制。
恶意用户可以利用这一点进行假人压测、干扰或盗号行为。

:::
**优点：**

* 离线可玩

**缺点：**

* 安全性低。
* 需要防范注册机器人。
* 需要防止假人压测。
* 无法自定义皮肤。
* 无法更改用户名。

## 自定义登录

自定义登录通常需要客户端安装 MOD 来辅助登录。根据登录时机的不同，它可以属于内置登录或外置登录。由于市场上自定义登录程序较少，安全性难以评估，这里不做详细讨论。

## 无登录

:::danger

不推荐在没有任何登录方式的情况下运行服务器，除非你完全清楚这样做的目的和风险。

:::

## 多重验证登录

多重验证登录是指服务器同时启用两种或多种验证方式，用户必须通过所有验证才能登录。这种方式通常适用于需要严格控制的账号 (比如管理员)，但对所有玩家启用会增加登录复杂性和成本。

## 混合验证登录

混合验证登录允许服务器提供多种验证方式，供玩家选择。

:::danger

这种方式适用于兼容多种登录方式的需求，但通常会增加维护难度和安全风险。

如果没有明确的需求，不建议使用混合验证登录。任何实现混合登录的尝试都可能增加维护成本和复杂性，增加安全隐患。

:::

### Floodgate(正版登录和基岩 (Xbox) 登录共存)

Floodgate 是一个允许使用 Minecraft 基岩版的游戏绕过 `通用外置登录` 直接加入游戏的逆天插件，它可以说也是一种混合验证登录插件，能让正版和基岩版共存。

其中基岩版的玩家 UUID 由 Floodgate 通过 `Xbox账号的XUID` 来生成，UUID 碰撞风险极低。

### 正版登录和离线 (内置) 登录共存

一般它指实现`使正版玩家自动登录、离线玩家则需要游戏内登录`的插件。

如果你想要正版玩家跳过登陆，试试登陆插件与 [FastLogin](https://www.spigotmc.org/resources/.14153) 插件搭配使用。

通常此类程序内置的账号管理系统已经十分完善，如果配置得当不太可能出现 UUID 碰撞的风险，除非它们自身就有问题。

### 正版登录和通用外置登录共存

:::danger
多外置共存问题最大，如果操作不当，可能导致一下后果：

1. 可能重名：

    > 在多外置共存的环境下，不同外置中的玩家可能会有相同的名字，但他们的 UUID 是不一样的。当这些玩家同时在线时可能会造成服务端整体系统的一些混淆。此外，一些不怀好意的用户可能故意使用与别人相同的名字来进行欺诈行为，这种重名和冒名的情况可能会导致安全和信任问题，给游戏环境带来潜在风险。

2. UUID 碰撞

    > 在多外置共存的环境下，UUID 碰撞的概率被放大了，而 UUID 相当于玩家的身份证，一旦出现 UUID 碰撞的话，可能会带来严重的后果。这将会导致玩家数据丢失设置错乱，并且排查和解决这些问题会极其困难，将导致灾难性的损失。

:::

相比于 `正版登录和离线(内置)登录共存` 以及 `Floodgate(正版登录和基岩版(Xbox)登录共存)`，他们两个通常都拥有专用的账号管理系统来防止玩家身份混淆和相关问题。然而，Yggdrasil 本身就是一个独立的账号管理系统，多外置之间通常无法直接通信和同步信息。因此在多外置系统共存的环境下，如果不设计一个完善的身份管理系统，极有可能出现上述后果，从而引发严重的安全和信任问题。

好在 UUID 碰撞概率相当的低，普通服主或管理员只需要考虑重名问题就好了，是吧。
