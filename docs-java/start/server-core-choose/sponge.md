---
title: Sponge
sidebar_position: 5
---

# Sponge

![](_images/sponge.png)

Sponge 有 SpongeForge(Forge+Sponge 插件) 和 SpongeVanilla(原版+Sponge 插件) 两种选择。

按照本文档的分类，SpongeForge 属于 [混合端](hybrid.md)，但是 Sponge 与与他们有 **极大的** 不同。

最明显的区别是，Sponge 另起炉灶，是完全独立的插件生态，且从设计之初就考虑到了插件与 mod 的兼容问题，
你不大可能看到插件和 mod 打架的情况，但劣势也在此处，相比 Spigot / Paper，Sponge 插件**十分稀少**。并且 Sponge 插件开发更加困难。

虽然 Sponge 有着较好的性能和完善的官方文档，但目前仍然不推荐新人使用此核心。

:::danger

Sponge 插件与 Bukkit 插件不兼容。

:::

:::info

`官网` https://spongepowered.org

`文档(英文)` https://docs.spongepowered.org/stable/en

`文档(中文)` https://docs.spongepowered.org/stable/zh-CN

:::

更推荐的新人入门教程：https://archives.mcbbs.co/read.php?tid=786074
