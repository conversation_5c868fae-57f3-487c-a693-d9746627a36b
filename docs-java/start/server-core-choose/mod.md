---
title: Mod 端
sidebar_position: 3
---

# Mod 端

Mod 端的选择一般优先考虑玩法（即主要的玩法 Mod），再考虑性能

比方说，我的服务器主要玩法是匠魂，它只支持 Forge，那我就只能选择 Forge

但如果我的服务器主要玩法是铁砧工艺，他同时支持 Forge 和 Fabric，那我应该选择优化 mod 更多的 Fabric。

如果后续想要更新版本，应该尽量选择对模组开发者更友好的 Forge/NeoForge

## 扩展

### NeoForge

![](_images/NeoForge.png)

关于 NeoForge 和 Forge 的关系，可以查看[这篇文章](https://www.bilibili.com/opus/827402209530478597)

通常来说，1.21 以上**可以**使用 NeoForge

### Sinytra Connector

![](_images/Sinytra_Connector.png)

Sinytra Connector(中文名：信雅互联) 可以在 Forge 上运行 Fabric 模组

:::warning

除非非常有必要，最好不要使用信雅互联，这会大大增加服务器崩溃概率

:::

[下载地址](https://www.mcmod.cn/class/11627.html)
，你还需要安装 [Forgified Fabric API](https://www.mcmod.cn/class/11464.html)

为了增加兼容性，还需要安装 [Connector Extras](https://www.mcmod.cn/class/12197.html)

### Kilt

Kilt 可以在 Fabric 上运行 Forge 模组

:::danger

使用此 mod 默认你已经疯了。

:::

[下载地址](https://www.mcmod.cn/class/16277.html)

### Quilt

![](_images/Quilt.svg)

建议直接用 Fabric

### Legacy Fabric

1.14 以下版本的 Fabric，主要支持 1.3-1.13.2 以及愚人节版本

[下载地址](https://www.mcmod.cn/class/3391.html)，还需要安装[Legacy Fabric API](https://www.mcmod.cn/class/3398.html)

:::warning

Legacy Fabric 的模组生态相较于 Forge 并不完善，不建议使用。

:::

其他整活版本：

| 名称                   | 兼容版本         | 下载地址                                |
|----------------------|--------------|-----------------------------------------------|
| Babric               | Beta 1.7.3   | [McMOD](https://www.mcmod.cn/class/6367.html) |
| Cursed Legacy Fabric | Beta 1.7.3   | [McMOD](https://www.mcmod.cn/class/3902.html) |
| Fabric Loader        | Alpha 1.2.2a | [McMOD](https://www.mcmod.cn/class/5753.html) |
