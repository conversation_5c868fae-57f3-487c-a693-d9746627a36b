---
title: 其他分支
sidebar_position: 2
---

# 其他分支

在 Folia 的开发历史上，还有许多其他分支，太多了，这里只列出了一些可能有用的

## Lumina

Lumina 是 LeavesMC 开发的一个 Folia 分支，旨在遵循原版生电特性的同时 Folia 的功能

:::tip

目前 Lumina 处于早期开发阶段，暂时不完善，如果你需要生电特性，推荐使用 [Lophine](luminol.md#lophine)

:::

### 下载

Lumina 目前在官网上不提供下载，你可以选择自己去编译，或使用第三方预编译版本

[下载 (1.20.6)](https://vip.123pan.cn/1821558579/Lingyi/aaa/lumina/Lumina-1.20.6-mcres.jar)

## DeerFolia

:::info

`GitHub` https://github.com/DeerGiteaMirror/DeerFolia

`MineBBS` https://www.minebbs.com/resources/.793

:::

一个专注于还原特性的分支，不会加其他的功能，更新速度快

下载 (官方):

* [1.21.1](https://ssl.lunadeer.cn:14446/zhangyuheng/DeerFolia/releases/download/1.21.1-83e1fe4/DeerFolia-1.21.1-83e1fe4.jar)
* [1.21](https://ssl.lunadeer.cn:14446/zhangyuheng/DeerFolia/releases/download/1.21-75ed34d/DeerFolia-1.21-75ed34d.jar)
* [1.20.6](https://ssl.lunadeer.cn:14446/zhangyuheng/DeerFolia/releases/download/1.20.6-85d30a2/DeerFolia-paperclip-1.20.6-R0.1-SNAPSHOT-mojmap.jar)
* [1.20.4](https://ssl.lunadeer.cn:14446/zhangyuheng/DeerFolia/releases/download/1.20.4-2615826/DeerFolia-paperclip-1.20.4-R0.1-SNAPSHOT-reobf.jar)

## DirtyFolia

:::info

`GitHub` https://github.com/pluralitycn/DirtyFolia

:::

一个类似于 DirtyMolia 的 Folia fork，不过有 1.19.4 版本，现在不更新了

下载镜像：

* [1.19.4](https://vip.123pan.cn/1821558579/Lingyi/aaa/dirtyfolia/dirtyfolia-1.19.4-mcres.cn.jar)
* [1.20.1](https://vip.123pan.cn/1821558579/Lingyi/aaa/dirtyfolia/dirtyfolia-1.20.1-mcres.cn.jar)

## Kaiiju

:::info

`GitHub` https://github.com/KaiijuMC/Kaiiju

:::

一个非常早的 Folia 分支，加入了许多优化和功能

下载镜像：

* [1.20.1](https://vip.123pan.cn/1821558579/Lingyi/kaiiju-paperclip-1.20.1-R0.1-SNAPSHOT-reobf.jar)
* [1.19.4](https://dl.8aka.org/plugins/kaiiju-paperclip-1.19.4-R0.1-SNAPSHOT-reobf.jar)
* [1.19.3](https://dl.8aka.org/plugins/kaiiju-paperclip-1.19.3-R0.1-SNAPSHOT-reobf.jar)
