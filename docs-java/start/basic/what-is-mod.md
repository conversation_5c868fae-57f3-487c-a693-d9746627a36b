---
title: 什么是模组？
sidebar_position: 5
---

# 什么是模组？

模组 ( Mod ) 是遵循 Forge、Fabric 或 LiteLoader 等 API 规范编写的 Java 程序。

## 工作形式

Minecraft 模组 ( Mod ) 一般来说模组会利用 Forge 或 Fabric 作为前置开发，通过反编译 Minecraft Java 源代码了解 Minecraft 干了什么，

进而删减或修改内容以实现新的功能。删减或修改内容既可以是服务端，也可以是客户端。因而相较插件只能修改部分服务端的内容要有更大的功能性。

## 安装位置

模组安装在服务器根目录的 `/mods` 文件夹，然后需要 **重启** 服务器。

## 模组的优点

* 可以更为随心所欲的修改包括客户端和服务端的游戏代码，功能性更多；
* 大多数大型 Mod 是有相对详细的 Wiki 供玩家参考，更方便入手；
* 可以控制客户端使用的 Mod 降低客户端使用作弊端的可能性；

## 模组的缺点

* 模组基本没有稳定的 API，不像插件依赖稳定的 API 的插件，如果你玩的模组相对小众，那么很可能只有特定几个版本能够找到；
* 服务端如果不加插件生态，管理方面的模组会少一些，如果加上 Bukkit 生态兼容又容易出现兼容性、报错等问题，服务端往往稳定性欠佳，基本只能开半年；
* 模组更容易出现不兼容的情况，有时由于版本等不兼容很容易导致客户端无法启动的问题；
* 客户端必须安装特定的模组才能进入服务器，这存在的便捷性和安全性问题对某些玩家来说是比较难以接受的；
* 关于玩法的模组性能往往比较拉胯 (特别是 Forge 平台)，对于 CPU 和内存的要求会更高。

## 模组可以做的事情

|性质 | 描述 | 样例 |
|:---:|:---|:---|
|探索 | 增加新的生态环境，生物，矿石，方块，建筑乃至新世界|`星系`|
|科技 | 增加近现代科技元素，如机器、交通、能源、农学、基因学等|`工业2`|
|魔法 | 增加魔法游戏元素|`神秘时代4`|
|仓储 | 增加新的储存容器，提高物品储存能力|`储存抽屉`|
|工匠 | 增加新的工具、盔甲、武器|`更多武器`|
|食物 | 增加食材与菜谱|``丰收工艺``|
|RPG|增加角色扮演或冒险类游戏元素|`暮色森林`|
|信息 | 增加玩家可以获取的信息，例如查询合成表，小地图，血条等|`Hwyla`|
|优化 | 优化内存和帧数，信息获取等|`FoamFix`|
