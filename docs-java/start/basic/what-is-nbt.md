---
title: 什么是 NBT？
sidebar_position: 9
---

## 概念

> NBT（二进制命名标签，NamedBinary Tags）格式是一种由众多的标签所组成的树状数据结构。在 Minecraft 中，其被广泛用于向存档文件中存储数据。所有的标签都有一个独立的数字 ID 和名称，以及一个负载。
>
> 另一种玩家更熟悉的是字符串形式的 NBT，通常在命令里使用。这种格式常被称为 SNBT（字符串化的二进制命名标签，Stringified NBT）。

用大白话说，就是描述：

- 某个方块 - （比如箱子，熔炉）装了什么，告示牌信息，命令方块里的命令，等等
- 某个实体 - 穿了什么，拿了什么，药水效果，着火，骑乘，等等
- 某个物品 - 名字，描述，附魔，属性，耐久损耗，CMD 标签，等等

## 参考

https://zh.minecraft.wiki/w/NBT%E6%A0%BC%E5%BC%8F
