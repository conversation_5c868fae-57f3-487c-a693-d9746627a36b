---
title: 常用服务器管理指令
sidebar_position: 10
---

# 常用服务器管理指令

欢迎开服，有一些常用服务器管理指令你必须了解，这是你开服的第一步!!

## /stop

这个指令是用来关闭服务器的，注意：请确定你需要关闭后再关闭，这次操作会踢出所有玩家

:::danger

千万不要强制关闭服务器，否则会造成存档丢失，玩家数据损坏等多种严重问题

:::

## /restart

:::note

使用前需要在服务器根目录的 `spigot.yml` 文件找到如下内容：

```yaml
  restart-script: ''
```

在引号内设置启动脚本路径，否则这个指令不会帮你自动重启服务器，你仍然需要自行启动服务端

:::

## /kick [玩家名称] \[踢出玩家的原因]\(可以不写)

踢出服务器上的玩家，当然，玩家可以重新加入

## /ban [玩家名称] \[封禁玩家的原因]\(可以不写)

封禁服务器上的玩家，除非解封，玩家不可以重新进入

## /pardon [玩家名称]

解封曾经封禁过的玩家，注意，在 MC 的老版本 (1.0.16 之前) 中这个指令可能是/unban

## /list

列出服务器上的所有玩家 (不包括已经退出服务器的)

## /ban-ip [IP 地址]

封禁服务器上的玩家 (根据 IP 地址)，这会导致封禁玩家所处的 IP 的玩家都不可进入

:::danger

不要尝试在 frp 上使用这个指令，你会惊奇的发现，所有人都进不去

:::

:::note

其实这玩意真的没多大用，对于玩家来说，重启一下路由器或挂个代理就可以

:::

## /pardon-ip [IP 地址]

解封封禁过 IP 地址的玩家

## /plugins

看到服务器所有安装的插件，这个指令的权限默认是打开的，所有人都能看到。

如果你不想玩家通过指令获取插件列表，可以将以下权限设置为 `false` 即可。

```yaml
bukkit.command.plugins
bukkit.command.version
bukkit.command.help
```

通过简单的权限设置，玩家将无法通过指令获取服务器的插件列表，但某些作弊客户端

能够通过分析 tab 补全，利用某些版本的漏洞，获取服务器插件列表。

如果介意被获取插件，可以安装 `Plugin Hide Pro` 或 `CommandWhiteist` 插件

## /op [玩家名称]

给予指定玩家 OP(操作员) 权限

## /deop [玩家名称]

解除玩家的 OP 权限

:::danger

千万不要随意给玩家 OP，恶意玩家可以使用此权限损坏服务器，甚至入侵主机。

需要注意，OP 间可以互相 deop，只要一个人获取了 OP 就能下掉其他所有的 OP，

这是一个非常危险的权限，如果可能，请用权限管理插件而不是给一堆 OP

:::
