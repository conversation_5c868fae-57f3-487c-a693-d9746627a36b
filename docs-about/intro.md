---
sidebar_position: 1
title: 关于我们
---

# 关于我们

```text
👋你好！我是 NitWikit 项目和 8aka-Team 团队的建立者驿站
```

![](_images/me.png)

~~早就想写这么一个页面结果一直拖着一直忘记~~

一开始，我计划是写在 [相亲墙](https://nitwikit.8aka.org/love) 的位置，但后来又扩充 Bedrock 版内容，不那么适合了

~~怎么到现在还是只有我挂在那墙上相亲啊~~

## Q&A

### 笨蛋文档名字从何而来？

打从一开始我就打算起一个让人耳目一新，一下就能记住的我的世界开服教程文档名字

但是“教程指北”之类的创意早被人使用过了，那段时间“笨蛋”一词有点成为口头禅，
再加上这个教程本来就是写给新手的，我就叫它笨蛋文档了，雪萌起了英文版名字 NitWikit

MC 里傻子村民的英文就是 nitwit，后缀又和 wiki 相近，所以组合成了 Nitwikit

### 发展历程？

在笨蛋文档诞生很早之前我就有已经有过编写开服教程的想法，那时尝试过石墨文档，语雀，自建 GitBook。不过他们都夭折了。。。

在笨蛋文档即将诞生的那段时间，我想编写一个文档来收录一些总有人问的问题，以及收录一些零碎的资源，下次回答别人问题的时候直接甩文档链接就好了，
又想到从前想写的开服教程，最终我决定编写一个从未有人做过的，详细无比的开服教程。

一开始我只是拉着人才（他写反作弊部分）编写笨蛋文档，部署在 GitHub pages，然后 lilingfeng 和小杰自己跑来问我能不能参与维护，
以及一些路人像是 gyc123456-1 来给我 pr，后面还有 lim114514 在我不知情的情况下帮我买好 yizhan.wiki 这个域名，雪萌帮我想英文名字，
画群头像，banner 图，MineBBS 宣传图，小涵想写 Bedrock 内容给文档拆成三块，突然冒出个糯米团子搞出个 VitePress 版文档，
有那么几个我不认识的人和我认识的三七为笨蛋文档录制视频，千屈帮助编写了好多好多内容，修了好多 bug，甚至重写文档整个界面等等。
不知不觉就有了六十多个贡献者，如果算上 GitHub 之外提供建议和帮助的人，远比这个数字要多。

## 联系我们

前往 https://8aka.org/qq
