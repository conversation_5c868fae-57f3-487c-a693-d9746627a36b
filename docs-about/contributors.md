---
sidebar_position: 3
title: 贡献者名单
---

import ContributorCard from '@site/src/components/ContributorCard';

# 贡献者名单

:::tip 项目贡献者
以下列表展示了所有为本项目做出贡献的开发者。我们对每一位贡献者表示衷心的感谢！
:::

## 贡献统计

本项目的所有贡献者数据都来自于 [GitHub 贡献者页面](https://github.com/8aka-Team/NitWikit/graphs/contributors)。数据包含了每位贡献者的提交次数、代码添加和删除行数等信息。

下面是贡献者的详细列表，贡献量仅代表贡献行数，不代表贡献质量，其排名仅供参考：

<ContributorCard repo="8aka-Team/NitWikit" />

## 如何参与贡献

我们欢迎并鼓励更多的人参与到文档的改进中来！如果你希望为本项目贡献内容，请查阅[贡献指南](https://github.com/8aka-Team/NitWikit/blob/main/CONTRIBUTING.md)。

参与贡献的方式包括但不限于：

1. **提交内容更新**：改进现有文档、修复错误或添加新内容
2. **提出建议**：对文档结构、内容组织或新功能提出建议
3. **改进代码**：优化网站代码，提高用户体验

### 贡献流程

1. Fork 本仓库到你的 GitHub 账户
2. 克隆你 Fork 的仓库到本地
   ```bash
   git clone https://github.com/你的用户名/NitWikit.git
   ```
   如果你对历史记录没有需求，建议这么克隆
   ```bash
   git clone https://github.com/你的用户名/NitWikit.git --depth 1
   ```
3. 创建新的分支
   ```bash
   git checkout -b feature/your-feature-name
   ```
4. 进行修改并提交
   ```bash
   git add .
   git commit -m "描述你的修改"
   ```
5. 推送到你的仓库
   ```bash
   git push origin feature/your-feature-name
   ```
6. 创建 Pull Request
