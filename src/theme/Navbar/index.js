import React, { useEffect, useState } from 'react';
import Navbar from '@theme-original/Navbar';
import GitHubButton from '@site/src/components/GitHubButton';
import AIChatButton from '@site/src/components/AIChatButton';
import AIChatModal from '@site/src/components/AIChatModal';
import { useThemeConfig } from '@docusaurus/theme-common';
import '@site/src/css/ai-chat.css';

export default function NavbarWrapper(props) {
  const {
    navbar: { items },
  } = useThemeConfig();

  // 追踪窗口宽度
  const [isMobile, setIsMobile] = useState(false);

  // AI Chat 状态
  const [isAIChatOpen, setIsAIChatOpen] = useState(false);

  // 过滤掉原始的GitHub链接项
  const filteredItems = items.filter(
    (item) => !(item.className === 'header-github-link' && item.href?.includes('github.com'))
  );

  // 监听窗口大小变化
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 996);
    };
    
    // 初始检查
    checkMobile();
    
    // 监听窗口大小变化
    window.addEventListener('resize', checkMobile);
    
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // 在DOM操作后插入GitHub按钮和AI Chat按钮（仅在非移动设备上）
  useEffect(() => {
    if (isMobile) {
      // 移动设备上，移除任何可能存在的按钮
      const existingGitHubButton = document.querySelector('#github-button-container');
      if (existingGitHubButton) {
        existingGitHubButton.parentNode.removeChild(existingGitHubButton);
      }
      const existingAIChatButton = document.querySelector('#ai-chat-button-container');
      if (existingAIChatButton) {
        existingAIChatButton.parentNode.removeChild(existingAIChatButton);
      }
      return;
    }
    
    const insertButtons = () => {
      // 找到主题切换按钮
      const colorModeToggle = document.querySelector('.navbar__items--right .colorModeToggle');
      if (!colorModeToggle) return;

      // 检查是否已经存在按钮
      const existingGitHubButton = document.querySelector('#github-button-container');
      const existingAIChatButton = document.querySelector('#ai-chat-button-container');

      if (!existingAIChatButton) {
        // 创建AI Chat按钮包装器
        const aiChatWrapper = document.createElement('div');
        aiChatWrapper.className = 'navbar__item';

        // 创建AI Chat按钮的容器
        const aiChatButtonContainer = document.createElement('div');
        aiChatButtonContainer.id = 'ai-chat-button-container';

        // 将AI Chat按钮容器添加到包装器中
        aiChatWrapper.appendChild(aiChatButtonContainer);

        // 将包装器添加到主题切换按钮前面
        colorModeToggle.parentNode.insertBefore(aiChatWrapper, colorModeToggle);

        // 渲染React组件到容器中
        const React = require('react');
        const ReactDOM = require('react-dom');
        ReactDOM.render(<AIChatButton onClick={() => setIsAIChatOpen(true)} />, aiChatButtonContainer);
      }

      if (!existingGitHubButton) {
        // 创建GitHub按钮包装器
        const githubWrapper = document.createElement('div');
        githubWrapper.className = 'navbar__item';

        // 创建GitHub按钮的容器
        const githubButtonContainer = document.createElement('div');
        githubButtonContainer.id = 'github-button-container';

        // 将GitHub按钮容器添加到包装器中
        githubWrapper.appendChild(githubButtonContainer);

        // 将包装器添加到主题切换按钮前面
        colorModeToggle.parentNode.insertBefore(githubWrapper, colorModeToggle);

        // 渲染React组件到容器中
        const React = require('react');
        const ReactDOM = require('react-dom');
        ReactDOM.render(<GitHubButton />, githubButtonContainer);
      }
    };
    
    // 初始插入和页面变化时重新插入
    insertButtons();

    // 监听导航栏的变化
    const observer = new MutationObserver(insertButtons);
    const navbar = document.querySelector('.navbar__items--right');
    if (navbar) {
      observer.observe(navbar, { childList: true, subtree: true });
    }

    return () => {
      // 组件卸载时停止观察
      observer.disconnect();
    };
  }, [isMobile]);  // 依赖isMobile，当设备类型变化时重新运行

  return (
    <>
      <Navbar {...props} items={filteredItems} />
      <AIChatModal
        isOpen={isAIChatOpen}
        onClose={() => setIsAIChatOpen(false)}
      />
    </>
  );
} 