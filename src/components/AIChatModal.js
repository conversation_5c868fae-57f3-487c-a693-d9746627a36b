import React, { useState, useRef, useEffect } from 'react';
import { useColorMode } from '@docusaurus/theme-common';

export default function AIChatModal({ isOpen, onClose }) {
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [engineMode, setEngineMode] = useState('multihop'); // 'multihop' or 'agent'
  const [currentQueryId, setCurrentQueryId] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const messagesEndRef = useRef(null);
  const wsRef = useRef(null);
  const { colorMode } = useColorMode();
  const isDarkTheme = colorMode === 'dark';

  // 生成随机UUID
  const generateUUID = () => {
    return '_' + Math.random().toString(36).substr(2, 9) + '-' + 
           Math.random().toString(36).substr(2, 4) + '-' + 
           Math.random().toString(36).substr(2, 4) + '-' + 
           Math.random().toString(36).substr(2, 4) + '-' + 
           Math.random().toString(36).substr(2, 12);
  };

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 清理WebSocket连接
  const cleanupWebSocket = () => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    setIsConnected(false);
  };

  // 创建对话
  const createConversation = async (userQuery, queryId) => {
    try {
      const response = await fetch('https://api.devin.ai/ada/query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          engine_id: engineMode,
          user_query: `<relevant_context>用户无法看到 markdown 文件,所以你需要给出完整的过程 </relevant_context>${userQuery}`,
          keywords: [],
          repo_names: ["8aka-Team/NitWikit"],
          additional_context: "",
          query_id: queryId,
          use_notes: false,
          generate_summary: false
        })
      });

      const result = await response.json();
      return result.status === 'success';
    } catch (error) {
      console.error('创建对话失败:', error);
      return false;
    }
  };

  // 连接WebSocket获取响应
  const connectWebSocket = (queryId) => {
    return new Promise((resolve, reject) => {
      const ws = new WebSocket(`wss://api.devin.ai/ada/ws/query/${queryId}`);
      wsRef.current = ws;
      
      let responseText = '';
      let hasStarted = false;

      ws.onopen = () => {
        setIsConnected(true);
        console.log('WebSocket连接已建立');
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          if (data.type === 'chunk') {
            if (!hasStarted) {
              hasStarted = true;
              // 添加AI响应消息
              setMessages(prev => [...prev, { 
                type: 'ai', 
                content: '', 
                isStreaming: true 
              }]);
            }
            
            responseText += data.data;
            
            // 更新最后一条AI消息
            setMessages(prev => {
              const newMessages = [...prev];
              const lastMessage = newMessages[newMessages.length - 1];
              if (lastMessage && lastMessage.type === 'ai') {
                lastMessage.content = responseText;
              }
              return newMessages;
            });
          }
        } catch (error) {
          console.error('解析WebSocket消息失败:', error);
        }
      };

      ws.onclose = () => {
        setIsConnected(false);
        setIsLoading(false);
        
        // 标记流式传输结束
        setMessages(prev => {
          const newMessages = [...prev];
          const lastMessage = newMessages[newMessages.length - 1];
          if (lastMessage && lastMessage.type === 'ai') {
            lastMessage.isStreaming = false;
          }
          return newMessages;
        });
        
        resolve();
      };

      ws.onerror = (error) => {
        console.error('WebSocket错误:', error);
        setIsConnected(false);
        setIsLoading(false);
        reject(error);
      };
    });
  };

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage = inputValue.trim();
    setInputValue('');
    setIsLoading(true);

    // 添加用户消息
    setMessages(prev => [...prev, { type: 'user', content: userMessage }]);

    try {
      let queryId;
      
      if (currentQueryId) {
        // 继续对话，使用现有的queryId
        queryId = currentQueryId;
      } else {
        // 新对话，生成新的queryId
        queryId = generateUUID();
        setCurrentQueryId(queryId);
      }

      // 创建对话
      const success = await createConversation(userMessage, queryId);
      
      if (success) {
        // 连接WebSocket获取响应
        await connectWebSocket(queryId);
      } else {
        throw new Error('创建对话失败');
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      setMessages(prev => [...prev, { 
        type: 'ai', 
        content: '抱歉，发生了错误，请稍后重试。', 
        isError: true 
      }]);
      setIsLoading(false);
    }
  };

  // 处理键盘事件
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 清空对话
  const handleClearChat = () => {
    setMessages([]);
    setCurrentQueryId(null);
    cleanupWebSocket();
  };

  // 关闭模态框时清理
  const handleClose = () => {
    cleanupWebSocket();
    onClose();
  };

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      cleanupWebSocket();
    };
  }, []);

  if (!isOpen) return null;

  return (
    <div className="ai-chat-modal-overlay" onClick={handleClose}>
      <div 
        className={`ai-chat-modal ${isDarkTheme ? 'dark' : 'light'}`} 
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 */}
        <div className="ai-chat-header">
          <div className="ai-chat-title">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="20" height="20">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10c1.54 0 3-.35 4.31-.99L22 22l-1.01-5.69C21.65 15 22 13.54 22 12c0-5.52-4.48-10-10-10zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
              <circle cx="8.5" cy="12" r="1.5"/>
              <circle cx="15.5" cy="12" r="1.5"/>
              <path d="M12 17.5c2.33 0 4.31-1.46 5.11-3.5H6.89c.8 2.04 2.78 3.5 5.11 3.5z"/>
            </svg>
            AI 对话助手
          </div>
          <div className="ai-chat-controls">
            <select 
              value={engineMode} 
              onChange={(e) => setEngineMode(e.target.value)}
              className="ai-chat-mode-select"
              disabled={isLoading}
            >
              <option value="multihop">快速思考</option>
              <option value="agent">深度思考</option>
            </select>
            <button 
              onClick={handleClearChat} 
              className="ai-chat-clear-btn"
              disabled={isLoading}
              title="清空对话"
            >
              🗑️
            </button>
            <button onClick={handleClose} className="ai-chat-close-btn">×</button>
          </div>
        </div>

        {/* 消息区域 */}
        <div className="ai-chat-messages">
          {messages.length === 0 && (
            <div className="ai-chat-welcome">
              <p>👋 你好！我是 NitWikit 的 AI 助手</p>
              <p>我可以帮你解答关于 Minecraft 服务器搭建的问题</p>
              <p>选择思考模式：<strong>快速思考</strong> 适合简单问题，<strong>深度思考</strong> 适合复杂问题</p>
            </div>
          )}
          
          {messages.map((message, index) => (
            <div key={index} className={`ai-chat-message ${message.type}`}>
              <div className="ai-chat-message-content">
                {message.content}
                {message.isStreaming && <span className="ai-chat-cursor">|</span>}
              </div>
            </div>
          ))}
          
          {isLoading && messages.length > 0 && !messages[messages.length - 1]?.isStreaming && (
            <div className="ai-chat-message ai">
              <div className="ai-chat-message-content">
                <div className="ai-chat-loading">正在思考中...</div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* 输入区域 */}
        <div className="ai-chat-input-area">
          <div className="ai-chat-input-container">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="输入你的问题..."
              className="ai-chat-input"
              disabled={isLoading}
              rows={1}
            />
            <button 
              onClick={handleSendMessage} 
              disabled={!inputValue.trim() || isLoading}
              className="ai-chat-send-btn"
            >
              {isLoading ? '...' : '发送'}
            </button>
          </div>
          {isConnected && (
            <div className="ai-chat-status">
              <span className="ai-chat-status-indicator">🟢</span>
              已连接
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
