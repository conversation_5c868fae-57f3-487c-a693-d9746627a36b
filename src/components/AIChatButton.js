import React from 'react';

export default function AIChatButton({ onClick }) {
  return (
    <button
      onClick={onClick}
      className="header-ai-chat-link ai-chat-icon-button"
      aria-label="AI 对话"
      title="AI 对话助手"
      style={{ 
        borderRadius: '50%', 
        overflow: 'hidden', 
        border: 'none', 
        boxShadow: 'none',
        background: 'transparent',
        cursor: 'pointer',
        padding: '0.4rem',
        width: '2.2rem',
        height: '2.2rem',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        transition: 'background-color 0.2s',
        color: 'var(--ifm-navbar-link-color)'
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="20" height="20">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10c1.54 0 3-.35 4.31-.99L22 22l-1.01-5.69C21.65 15 22 13.54 22 12c0-5.52-4.48-10-10-10zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
        <circle cx="8.5" cy="12" r="1.5"/>
        <circle cx="15.5" cy="12" r="1.5"/>
        <path d="M12 17.5c2.33 0 4.31-1.46 5.11-3.5H6.89c.8 2.04 2.78 3.5 5.11 3.5z"/>
      </svg>
    </button>
  );
}
