/* 赞助卡片容器 */
.sponsor-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

/* 单个赞助卡片 */
.sponsor-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  border-radius: 12px;
  background: var(--ifm-card-background-color, var(--ifm-background-surface-color));
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-align: center;
}

/* 卡片悬停效果 */
.sponsor-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

/* 卡片底部渐变色条 */
.sponsor-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--ifm-color-primary), var(--ifm-color-primary-lighter));
  transition: opacity 0.3s ease;
  opacity: 0.7;
}

.sponsor-card:hover::after {
  opacity: 1;
}

/* 头像容器 */
.sponsor-avatar-wrapper {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 1rem auto; /* 居中并保持底部间距 */
  border: 3px solid var(--ifm-color-primary-lightest);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.sponsor-card:hover .sponsor-avatar-wrapper {
  transform: scale(1.05);
}

/* 头像样式 */
.sponsor-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
  display: block;
}

.sponsor-card:hover .sponsor-avatar {
  transform: scale(1.1);
}

/* 赞助者信息 */
.sponsor-info {
  width: 100%;
}

/* 赞助者名称 */
.sponsor-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--ifm-heading-color);
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

.sponsor-card:hover .sponsor-name {
  color: var(--ifm-color-primary);
}

/* 赞助金额 */
.sponsor-amount {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--ifm-color-primary);
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  background: var(--ifm-color-primary-lightest);
  display: inline-block;
  font-size: 0.9rem;
}

/* 不同级别的赞助金额样式 */
.amount-s {
  background: rgba(156, 163, 175, 0.15);
  color: #6b7280;
}

.amount-m {
  background: rgba(249, 115, 22, 0.15);
  color: #d97706;
}

.amount-l {
  background: rgba(37, 99, 235, 0.15);
  color: #2563eb;
}

.amount-xl {
  background: rgba(220, 38, 38, 0.15);
  color: #dc2626;
}

/* 赞助备注 */
.sponsor-note {
  font-size: 0.9rem;
  color: var(--ifm-color-emphasis-700);
  margin-top: 0.5rem;
  line-height: 1.4;
  font-style: italic;
}

/* 空状态提示 */
.sponsor-empty {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--ifm-color-emphasis-600);
  font-style: italic;
  background: var(--ifm-card-background-color, var(--ifm-background-surface-color));
  border-radius: 12px;
  border: 1px dashed var(--ifm-color-emphasis-300);
}

/* 暗色模式适配 */
html[data-theme='dark'] .sponsor-card {
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
}

html[data-theme='dark'] .sponsor-card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.25);
}

html[data-theme='dark'] .sponsor-avatar-wrapper {
  border-color: var(--ifm-color-primary-darker);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 移动端适配 */
@media (max-width: 576px) {
  .sponsor-container {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
  
  .sponsor-card {
    padding: 1rem;
  }
  
  .sponsor-avatar-wrapper {
    width: 60px;
    height: 60px;
    margin: 0 auto 0.75rem auto; /* 调整移动端的margin */
  }
  
  .sponsor-name {
    font-size: 1rem;
  }
  
  .sponsor-amount {
    font-size: 0.8rem;
    padding: 0.2rem 0.5rem;
  }
} 