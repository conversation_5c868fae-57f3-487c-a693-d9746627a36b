/* GitHub图标基本样式 - 确保圆形 */
.header-github-link,
.github-icon-button {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0.4rem !important;
    width: 2.2rem !important;
    height: 2.2rem !important;
    border-radius: 50% !important; /* 确保圆形 */
    transition: background-color 0.2s !important;
    color: var(--ifm-navbar-link-color) !important;
    position: relative !important;
    overflow: hidden !important;
    /* 明确覆盖卡片样式 */
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
}

/* 移除任何可能的伪元素 */
.header-github-link:before,
.header-github-link:after,
.github-icon-button:before,
.github-icon-button:after {
    display: none !important;
    content: none !important;
    opacity: 0 !important;
    height: 0 !important;
    background: none !important;
}

/* 确保不会继承卡片样式 */
.header-github-link.card,
.header-github-link[class*="card"],
.github-icon-button.card,
.github-icon-button[class*="card"] {
    border: none !important;
    box-shadow: none !important;
    padding: 0.4rem !important;
    background: transparent !important;
}

.header-github-link:hover,
.github-icon-button:hover {
    background-color: var(--ifm-color-emphasis-200) !important;
    color: var(--ifm-navbar-link-color) !important;
    transform: none !important;
    box-shadow: none !important;
    border: none !important;
}

.header-github-link svg,
.github-icon-button svg {
    width: 1.7rem !important;
    height: 1.7rem !important;
    fill: currentColor !important;
}

/* 深色模式下的特定样式 */
html[data-theme='dark'] .header-github-link,
html[data-theme='dark'] .github-icon-button {
    color: var(--ifm-navbar-link-color) !important;
}

html[data-theme='dark'] .header-github-link:hover,
html[data-theme='dark'] .github-icon-button:hover {
    background-color: var(--ifm-color-emphasis-200) !important;
}

/* 移动设备上的GitHub图标样式 - 完全隐藏 */
@media (max-width: 996px) {
    .header-github-link,
    .github-icon-button,
    #github-button-container,
    .navbar__items--right .header-github-link,
    .navbar__items--right .github-icon-button {
        display: none !important;
        width: 0 !important;
        height: 0 !important;
        padding: 0 !important;
        margin: 0 !important;
        opacity: 0 !important;
        visibility: hidden !important;
        position: absolute !important;
        pointer-events: none !important;
    }
}