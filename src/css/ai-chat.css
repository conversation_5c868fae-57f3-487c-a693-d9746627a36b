/* AI <PERSON><PERSON> Styles */
.header-ai-chat-link,
.ai-chat-icon-button {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0.4rem !important;
    width: 2.2rem !important;
    height: 2.2rem !important;
    border-radius: 50% !important;
    transition: background-color 0.2s !important;
    color: var(--ifm-navbar-link-color) !important;
    position: relative !important;
    overflow: hidden !important;
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    cursor: pointer !important;
}

.header-ai-chat-link:hover,
.ai-chat-icon-button:hover {
    background-color: var(--ifm-color-emphasis-200) !important;
    color: var(--ifm-navbar-link-hover-color) !important;
}

/* AI Chat Modal Overlay */
.ai-chat-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    padding: 1rem;
}

/* AI Chat <PERSON> */
.ai-chat-modal {
    background: var(--ifm-background-color);
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    width: 100%;
    max-width: 600px;
    height: 80vh;
    max-height: 700px;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--ifm-color-emphasis-200);
}

.ai-chat-modal.dark {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* AI Chat Header */
.ai-chat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--ifm-color-emphasis-200);
    background: var(--ifm-color-emphasis-100);
}

.ai-chat-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--ifm-color-content);
}

.ai-chat-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ai-chat-mode-select {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--ifm-color-emphasis-300);
    border-radius: 6px;
    background: var(--ifm-background-color);
    color: var(--ifm-color-content);
    font-size: 0.875rem;
}

.ai-chat-clear-btn {
    padding: 0.25rem 0.5rem;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.ai-chat-clear-btn:hover {
    background: var(--ifm-color-emphasis-200);
}

.ai-chat-close-btn {
    width: 2rem;
    height: 2rem;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--ifm-color-content-secondary);
    transition: all 0.2s;
}

.ai-chat-close-btn:hover {
    background: var(--ifm-color-emphasis-200);
    color: var(--ifm-color-content);
}

/* AI Chat Messages */
.ai-chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.ai-chat-welcome {
    text-align: center;
    color: var(--ifm-color-content-secondary);
    padding: 2rem 1rem;
}

.ai-chat-welcome p {
    margin: 0.5rem 0;
}

.ai-chat-message {
    display: flex;
    flex-direction: column;
}

.ai-chat-message.user {
    align-items: flex-end;
}

.ai-chat-message.ai {
    align-items: flex-start;
}

.ai-chat-message-content {
    max-width: 80%;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    word-wrap: break-word;
    white-space: pre-wrap;
    position: relative;
}

.ai-chat-message.user .ai-chat-message-content {
    background: var(--ifm-color-primary);
    color: white;
    border-bottom-right-radius: 4px;
}

.ai-chat-message.ai .ai-chat-message-content {
    background: var(--ifm-color-emphasis-100);
    color: var(--ifm-color-content);
    border-bottom-left-radius: 4px;
}

.ai-chat-cursor {
    animation: blink 1s infinite;
    color: var(--ifm-color-primary);
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.ai-chat-loading {
    color: var(--ifm-color-content-secondary);
    font-style: italic;
}

/* AI Chat Input Area */
.ai-chat-input-area {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--ifm-color-emphasis-200);
    background: var(--ifm-background-color);
}

.ai-chat-input-container {
    display: flex;
    gap: 0.5rem;
    align-items: flex-end;
}

.ai-chat-input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--ifm-color-emphasis-300);
    border-radius: 8px;
    background: var(--ifm-background-color);
    color: var(--ifm-color-content);
    resize: none;
    min-height: 2.5rem;
    max-height: 6rem;
    font-family: inherit;
    font-size: 0.875rem;
    line-height: 1.4;
}

.ai-chat-input:focus {
    outline: none;
    border-color: var(--ifm-color-primary);
    box-shadow: 0 0 0 2px rgba(37, 194, 160, 0.2);
}

.ai-chat-send-btn {
    padding: 0.75rem 1.5rem;
    background: var(--ifm-color-primary);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s;
    min-width: 4rem;
}

.ai-chat-send-btn:hover:not(:disabled) {
    background: var(--ifm-color-primary-dark);
    transform: translateY(-1px);
}

.ai-chat-send-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.ai-chat-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--ifm-color-content-secondary);
}

.ai-chat-status-indicator {
    font-size: 0.5rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .ai-chat-modal {
        width: 100%;
        height: 100vh;
        max-height: 100vh;
        border-radius: 0;
        margin: 0;
    }
    
    .ai-chat-modal-overlay {
        padding: 0;
    }
    
    .ai-chat-message-content {
        max-width: 90%;
    }
    
    .ai-chat-header {
        padding: 1rem;
    }
    
    .ai-chat-input-area {
        padding: 1rem;
    }
}

/* Scrollbar Styling */
.ai-chat-messages::-webkit-scrollbar {
    width: 6px;
}

.ai-chat-messages::-webkit-scrollbar-track {
    background: var(--ifm-color-emphasis-100);
}

.ai-chat-messages::-webkit-scrollbar-thumb {
    background: var(--ifm-color-emphasis-300);
    border-radius: 3px;
}

.ai-chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--ifm-color-emphasis-400);
}
