# yaml-language-server: $schema=https://huacnlee.github.io/autocorrect/schema.json
rules:
  # Auto add spacing between CJK (Chinese, Japanese, Korean) and English words.
  # 0 - off, 1 - error, 2 - warning
  space-word: 1
  # Add space between some punctuations.
  space-punctuation: 1
  # Add space between brackets (), [] when near the CJK.
  space-bracket: 1
  # Add space between ``, when near the CJK.
  space-backticks: 1
  # Add space between dash `-`
  space-dash: 1
  # Add space between dollar $ when near the CJK.
  space-dollar: 0
  # Convert to fullwidth.
  fullwidth: 1
  # To remove space near the fullwidth punctuations.
  no-space-fullwidth: 1
  # To remove space arouned the fullwidth quotes “”, ''.
  no-space-fullwidth-quote: 1
  # Fullwidth alphanumeric characters to halfwidth.
  halfwidth-word: 1
  # Fullwidth punctuations to halfwidth in english.
  halfwidth-punctuation: 1
  # Spellcheck
  spellcheck: 0
# Enable or disable in special context
context:
  # Enable or disable to format codeblock in Markdown or AsciiDoc etc.
  codeblock: 1
textRules:
  # Config some special rule for some texts
  # For example, if we wants to let "Hello你好" just warning, and "Hi你好" to ignore
  # "Hello你好": 2
  # "Hi你好": 0
  ".中国": 0
fileTypes:
  # Config the files associations, you config is higher priority than default.
  # "rb": ruby
  # "Rakefile": ruby
  # "*.js": javascript
  # ".mdx": markdown
spellcheck:
  words:
    # Please do not add a general English word (eg. apple, python) here.
    # Users can add their special words to their .autocorrectrc file by their need.
    - ActiveMQ
    - AirPods
    - Aliyun
    - API
    - App Store
    - AppKit
    - AppStore = App Store
    - AWS
    - CacheStorage
    - CDN
    - CentOS
    - CloudFront
    - CORS
    - CPU
    - DNS
    - Elasticsearch
    - ESLint
    - Facebook
    - GeForce
    - GitHub
    - Google
    - GPU
    - H5
    - Hadoop
    - HBase
    - HDFS
    - HKEX
    - HTML
    - HTTP
    - HTTPS
    - I10n
    - I18n
    - iMovie
    - IndexedDB
    - Intel
    - iOS
    - iPad
    - iPadOS
    - iPhone
    - iTunes
    - JavaScript
    - jQuery
    - JSON
    - JWT
    - Linux
    - LocalStorage
    - macOS
    - Markdown
    - Microsoft
    - MongoDB
    - Mozilla
    - MVC
    - MySQL
    - Nasdaq
    - Netflix
    - NodeJS = Node.js
    - NoSQL
    - NVDIA
    - NYSE
    - OAuth
    - Objective-C
    - OLAP
    - OSS
    - P2P
    - PaaS
    - RabbitMQ
    - Redis
    - RESTful
    - RSS
    - RubyGem
    - RubyGems
    - SaaS
    - Sass
    - SDK
    - Shopify
    - SQL
    - SQLite
    - SQLServer
    - SSL
    - Tesla
    - TikTok
    - tvOS
    - TypeScript
    - Ubuntu
    - UML
    - URI
    - URL
    - VIM
    - watchOS
    - WebAssembly
    - WebKit
    - Webpack
    - Wi-Fi
    - Windows
    - WWDC
    - Xcode
    - XML
    - YAML
    - YML
    - YouTube
