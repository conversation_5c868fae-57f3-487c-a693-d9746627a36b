name: Test

on:
  push:
    branches:
      - 'main'
  pull_request:
    types: [opened, reopened, synchronize]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      # 拉取代码
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 1
      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV
      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: "Install"
        run: "pnpm install --frozen-lockfile"

      # 压缩图片
      - name: "图片压缩"
        uses: calibreapp/image-actions@main
        with:
          githubToken: ${{ secrets.GITHUB_TOKEN }}
          jpegQuality: '80'
          pngQuality: '80'
          webpQuality: '80'
          ignorePaths: 'node_modules/**'
          compressOnly: true

      - name: "MarkdownLint Check"
        uses: DavidAnson/markdownlint-cli2-action@v17
        with:
          fix: true
          globs: 'docs*/**/*.md'

      - name: "Build"
        run: "pnpm run build"