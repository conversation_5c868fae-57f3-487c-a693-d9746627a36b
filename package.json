{"name": "Wiki", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "prebuild": "node scripts/fetchContributors.js", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "test": "docusaurus build && docusaurus serve", "format": "markdownlint-cli2 --fix --glob 'docs*/**/*.md'", "format:check": "markdownlint-cli2 --glob 'docs*/**/*.md'", "autocorrect:lint": "autocorrect --lint docs docs-about docs-java docs-bedrock", "autocorrect:fix": "autocorrect --fix docs docs-about docs-java docs-bedrock", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@docusaurus/core": "^3.8.1", "@docusaurus/faster": "^3.8.1", "@docusaurus/plugin-content-docs": "^3.8.1", "@docusaurus/plugin-debug": "^3.8.1", "@docusaurus/plugin-google-tag-manager": "^3.8.1", "@docusaurus/plugin-pwa": "^3.8.1", "@docusaurus/plugin-sitemap": "^3.8.1", "@docusaurus/preset-classic": "^3.8.1", "@docusaurus/theme-common": "^3.8.1", "@docusaurus/theme-mermaid": "^3.8.1", "@docusaurus/theme-search-algolia": "^3.8.1", "@giscus/react": "^3.1.0", "@gracefullight/docusaurus-plugin-microsoft-clarity": "^1.0.0", "@mdx-js/react": "^3.1.0", "antd": "^5.26.0", "clsx": "^2.1.1", "docusaurus": "^1.14.7", "docusaurus-plugin-image-zoom": "^2.0.0", "docusaurus-plugin-sass": "^0.2.6", "mermaid": "^11.6.0", "mitt": "^3.0.1", "prism-react-renderer": "^2.4.1", "react": "^19.1.0", "react-dom": "^19.1.0", "sass": "^1.89.2", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@docusaurus/module-type-aliases": "^3.8.1", "@docusaurus/types": "^3.8.1", "@tailwindcss/typography": "^0.5.16", "autocorrect-node": "^2.14.0", "markdownlint-cli2": "^0.14.0", "node-fetch": "^2.7.0", "swc-loader": "^0.2.6"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=18.0"}, "packageManager": "pnpm@10.12.1"}