# 创建对话

POST https://api.devin.ai/ada/query

Body:

```json5
{
  "engine_id": "multihop",
  // 支持两种 multihop 为快速思考,agent 为深度思考
  "user_query": "<relevant_context>用户无法看到 markdown 文件,所以你需要给出完整的过程 </relevant_context>{用户的问题}",
  "keywords": [],
  "repo_names": [
    "8aka-Team/NitWikit"
  ],
  "additional_context": "",
  "query_id": "_9a6e3864-8714-4c62-ba19-7e9e9d88b437",
  // 需要随机生成一个ID
  "use_notes": false,
  "generate_summary": false
}
```

返回:

```json5
{
  "status": "success"
}
```

为成功创建

# 获取对话

GET wss://api.devin.ai/ada/ws/query/<ID>

流式返回

返回格式像这样:

```json5
{
  "type": "chunk",
  "data": "交互组件来增强用户体验。"
}
```

type != chunk 的不用理会

注意,agent 模式有思考过程,在第一个 type = file_contents 之前都是思考过程

# 继续对话

和创建对话一样的借口,不过不需要生成 UUID
